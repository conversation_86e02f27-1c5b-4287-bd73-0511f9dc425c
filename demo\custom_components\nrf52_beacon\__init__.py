import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.const import CONF_ID
from esphome.core import CORE

DEPENDENCIES = ["nrf52"]
CODEOWNERS = ["@your-username"]

nrf52_beacon_ns = cg.esphome_ns.namespace("nrf52_beacon")
NRF52Beacon = nrf52_beacon_ns.class_("NRF52Beacon", cg.Component)

CONF_UUID = "uuid"
CONF_MAJOR = "major"
CONF_MINOR = "minor"
CONF_TX_POWER = "tx_power"
CONF_ADVERTISING_INTERVAL = "advertising_interval"

CONFIG_SCHEMA = cv.Schema(
    {
        cv.GenerateID(): cv.declare_id(NRF52Beacon),
        cv.Optional(CONF_UUID, default="c29ce823-e67a-4e71-bff2-abaa32e77a98"): cv.string,
        cv.Optional(CONF_MAJOR, default=10): cv.uint16_t,
        cv.Optional(CONF_MINOR, default=1): cv.uint16_t,
        cv.Optional(CONF_TX_POWER, default=-12): cv.int_range(min=-40, max=8),
        cv.Optional(CONF_ADVERTISING_INTERVAL, default="1000ms"): cv.positive_time_period_milliseconds,
    }
).extend(cv.COMPONENT_SCHEMA)


async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)

    cg.add(var.set_uuid(config[CONF_UUID]))
    cg.add(var.set_major(config[CONF_MAJOR]))
    cg.add(var.set_minor(config[CONF_MINOR]))
    cg.add(var.set_tx_power(config[CONF_TX_POWER]))
    cg.add(var.set_advertising_interval(config[CONF_ADVERTISING_INTERVAL]))

    # Add Zephyr Bluetooth configuration
    if CORE.using_esp_idf:
        # This is for NRF52 with Zephyr, not ESP-IDF
        pass
    else:
        # Add Zephyr BLE configuration
        cg.add_platformio_option("lib_deps", ["zephyr-bluetooth"])
        cg.add_build_flag("-DCONFIG_BT=y")
        cg.add_build_flag("-DCONFIG_BT_PERIPHERAL=y")
        cg.add_build_flag("-DCONFIG_BT_DEVICE_NAME_DYNAMIC=y")
