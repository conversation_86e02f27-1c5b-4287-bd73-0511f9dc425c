# RTT故障排除指南

## 问题：RTT没有输出

如果你的RTT没有输出，请按以下步骤排查：

### 1. 检查硬件连接

确保J-Link调试器正确连接：
```
J-Link    NRF52833
------    --------
VCC   ->  3.3V
GND   ->  GND
SWDIO ->  SWDIO
SWCLK ->  SWCLK
```

### 2. 验证J-Link连接

使用J-Link Commander测试连接：
```bash
JLinkExe
connect
NRF52833_XXAA
SWD
4000
```

应该看到类似输出：
```
Connecting to target via SWD
Found SW-DP with ID 0x2BA01477
Scanning AP map to find all available APs
AP[1]: Stopped AP scan as end of AP map has been reached
AP[0]: AHB-AP (IDR: 0x24770011)
Iterating through AP map to find AHB-AP to use
AP[0]: Core found
AP[0]: AHB-AP ROM base: 0xE00FF000
CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
Found Cortex-M4 r0p1, Little endian.
```

### 3. 检查RTT Viewer设置

**RTT Viewer配置：**
- Target Device: `NRF52833_XXAA`
- Target Interface: `SWD`
- Speed: `4000 kHz`
- RTT Control Block: `Auto Detection`

**重要：** 确保在设备运行时连接RTT Viewer，不要在设备启动前连接。

### 4. 使用J-Link RTT Client

如果RTT Viewer不工作，尝试命令行工具：

**终端1 - 启动J-Link:**
```bash
JLinkExe
connect
NRF52833_XXAA
SWD
4000
```

**终端2 - 启动RTT Client:**
```bash
JLinkRTTClient
```

### 5. 检查RTT控制块

在J-Link Commander中检查RTT控制块：
```bash
# 在JLinkExe中执行
mem 0x20000000, 0x1000
```

查找RTT控制块标识符 "SEGGER RTT"。

### 6. 手动指定RTT地址

如果自动检测失败，可以手动指定RTT控制块地址：

在RTT Viewer中：
- 取消选择 "Auto Detection"
- 设置 "Address": `0x20000000` (或从内存转储中找到的地址)
- 设置 "Size": `0x1000`

### 7. 验证编译配置

检查编译输出中是否包含RTT相关的定义：
```bash
esphome compile nrf52833_simple.yaml -v
```

查找以下标志：
- `-DCONFIG_USE_SEGGER_RTT=1`
- `-DCONFIG_RTT_CONSOLE=1`
- `-DCONFIG_LOG_BACKEND_RTT=1`

### 8. 检查Zephyr配置

确保 `prj.conf` 文件在正确位置并包含：
```
CONFIG_USE_SEGGER_RTT=y
CONFIG_RTT_CONSOLE=y
CONFIG_LOG_BACKEND_RTT=y
```

### 9. 测试基本RTT功能

创建一个最小测试：

```yaml
# 最小RTT测试配置
esphome:
  name: rtt-test
  includes:
    - rtt_init.h
  on_boot:
    then:
      - lambda: |-
          #ifdef USE_NRF52
          extern int SEGGER_RTT_printf(unsigned BufferIndex, const char * sFormat, ...);
          SEGGER_RTT_printf(0, "Direct RTT test message\n");
          #endif

nrf52:
  board: adafruit_feather_nrf52840
  bootloader: mcuboot

logger:
  level: DEBUG
  baud_rate: 0
```

### 10. 常见问题和解决方案

**问题：RTT Viewer显示 "No RTT Control Block found"**
- 解决：确保设备正在运行，RTT在设备启动后才可用
- 尝试手动指定RTT地址

**问题：连接后立即断开**
- 解决：检查J-Link驱动程序版本
- 尝试降低SWD速度到1000 kHz

**问题：部分日志丢失**
- 解决：增加RTT缓冲区大小
- 在 `prj.conf` 中设置 `CONFIG_SEGGER_RTT_BUFFER_SIZE_UP=4096`

**问题：日志输出延迟**
- 解决：启用立即模式
- 添加 `CONFIG_LOG_MODE_IMMEDIATE=y`

### 11. 调试步骤

1. **验证设备运行：** LED应该闪烁或有其他运行指示
2. **检查J-Link连接：** 使用J-Link Commander验证连接
3. **启动RTT工具：** 在设备运行时启动RTT Viewer
4. **查看内存：** 检查RTT控制块是否存在
5. **测试直接输出：** 使用SEGGER_RTT_printf直接输出

### 12. 替代方案

如果RTT仍然不工作，可以考虑：

1. **使用UART输出：** 临时启用UART日志进行调试
2. **使用LED指示：** 通过LED闪烁模式指示状态
3. **使用GPIO输出：** 通过GPIO引脚输出调试信号

### 13. 有用的命令

**检查RTT状态：**
```bash
# 在J-Link Commander中
rtt start
rtt status
```

**保存RTT日志到文件：**
```bash
JLinkRTTLogger -Device NRF52833_XXAA -if SWD -Speed 4000 -RTTChannel 0 debug.log
```

**实时监控多个RTT通道：**
```bash
JLinkRTTClient -RTTTelnetPort 19021
```

如果按照以上步骤仍然无法解决问题，请检查：
- J-Link固件版本
- 目标设备是否正确
- 电源供应是否稳定
- SWD信号完整性
