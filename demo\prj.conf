# Zephyr project configuration for NRF52833 with RTT support

# Enable logging with RTT
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=4
CONFIG_LOG_MAX_LEVEL=4
CONFIG_LOG_MODE_IMMEDIATE=y

# Enable RTT (Real Time Transfer) for J-Link debugging
CONFIG_USE_SEGGER_RTT=y
CONFIG_RTT_CONSOLE=y
CONFIG_UART_CONSOLE=n

# RTT logging backend - ensure it's the primary backend
CONFIG_LOG_BACKEND_RTT=y
CONFIG_LOG_BACKEND_UART=n
CONFIG_LOG_BACKEND_SHOW_COLOR=n
CONFIG_LOG_BACKEND_FORMAT_TIMESTAMP=y

# RTT configuration - increase buffer sizes
CONFIG_SEGGER_RTT_MAX_NUM_UP_BUFFERS=3
CONFIG_SEGGER_RTT_MAX_NUM_DOWN_BUFFERS=3
CONFIG_SEGGER_RTT_BUFFER_SIZE_UP=2048
CONFIG_SEGGER_RTT_BUFFER_SIZE_DOWN=32

# Console configuration
CONFIG_CONSOLE=y
CONFIG_CONSOLE_SUBSYS=y

# Ensure RTT is initialized early
CONFIG_LOG_BACKEND_RTT_MODE_DROP=y
CONFIG_LOG_BACKEND_RTT_OUTPUT_BUFFER_SIZE=16

# Enable BLE support
CONFIG_BT=y
CONFIG_BT_PERIPHERAL=y
CONFIG_BT_DEVICE_NAME_DYNAMIC=y
CONFIG_BT_DEVICE_NAME="NRF52833-Beacon"
CONFIG_BT_DEVICE_NAME_MAX=32

# BLE advertising
CONFIG_BT_BROADCASTER=y
CONFIG_BT_EXT_ADV=n

# Power management
CONFIG_PM=y
CONFIG_PM_DEVICE=y

# GPIO support
CONFIG_GPIO=y

# ADC support for battery monitoring
CONFIG_ADC=y

# MCUboot support for OTA
CONFIG_BOOTLOADER_MCUBOOT=y

# Memory optimization
CONFIG_MAIN_STACK_SIZE=2048
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=1024
