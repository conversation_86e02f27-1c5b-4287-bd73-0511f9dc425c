# NRF52833 Beacon Demo with BLE OTA

这是一个基于ESPHome的NRF52833 beacon设备配置，支持电池供电和BLE OTA更新。

## 功能特性

- ✅ **BLE OTA支持**: 使用MCUboot + mcumgr协议
- ✅ **电池监控**: 实时监测电池电压
- ✅ **低功耗设计**: 优化的电源管理
- ✅ **Key数据广播**: 在BLE广播中实时反馈key数据
- ✅ **状态指示**: LED状态指示
- ⚠️ **BLE Beacon**: 需要自定义组件实现

## 硬件要求

- NRF52833或兼容的开发板
- 电池供电系统
- 状态指示LED（可选）
- SWD调试器（用于初始烧录）

## 配置文件说明

### 主配置文件: `nrf52833_demo.yaml`

```yaml
nrf52:
  board: adafruit_feather_nrf52840  # 根据实际板子调整
  bootloader: mcuboot  # 必须使用mcuboot才支持BLE OTA
```

### 关键配置项

1. **Bootloader配置**
   - 使用`mcuboot`而不是`adafruit`
   - 这会生成`app_update.bin`文件用于BLE OTA

2. **电池监控**
   - 使用NRF52内置ADC监控VDD
   - 低电量警告机制

3. **传感器数据**
   - 模板传感器模拟key数据
   - 可替换为实际的传感器读取逻辑

## BLE OTA使用方法

### 1. 初始烧录

```bash
# 编译配置
esphome compile nrf52833_demo.yaml

# 使用SWD烧录器烧录.hex文件
# 或使用pyocd
pyocd flash -t nrf52833 .esphome/build/nrf52833-beacon/zephyr/zephyr.hex
```

### 2. BLE OTA更新

1. **准备更新文件**
   - 编译后会生成`app_update.bin`文件
   - 位置: `.esphome/build/nrf52833-beacon/zephyr/app_update.bin`

2. **使用mcumgr-web工具**
   - 访问: https://mcumgr-web.netlify.app/
   - 点击"Connect"连接到设备
   - 选择"Image Upload"
   - 上传`app_update.bin`文件
   - 等待更新完成并重启

3. **使用命令行工具**
   ```bash
   # 安装mcumgr
   go install github.com/apache/mynewt-mcumgr-cli/mcumgr@latest
   
   # 通过BLE上传固件
   mcumgr --conntype ble --connstring peer_name=nrf52833-beacon image upload app_update.bin
   mcumgr --conntype ble --connstring peer_name=nrf52833-beacon image test <hash>
   mcumgr --conntype ble --connstring peer_name=nrf52833-beacon reset
   ```

## BLE Beacon实现

当前配置不包含完整的BLE beacon实现，因为ESPHome还没有内置的NRF52 BLE beacon支持。

### 自定义组件方案

提供了示例自定义组件文件：
- `custom_nrf52_beacon.h` - 头文件
- `custom_nrf52_beacon.cpp` - 实现文件

要使用自定义组件：

1. 创建`custom_components/nrf52_beacon/`目录
2. 将头文件和源文件放入该目录
3. 在YAML中启用外部组件：

```yaml
external_components:
  - source:
      type: local
      path: custom_components
    components: [ nrf52_beacon ]

nrf52_beacon:
  uuid: "c29ce823-e67a-4e71-bff2-abaa32e77a98"
  major: 10
  minor: 1
  tx_power: -12
  advertising_interval: 1000ms
```

## 电源优化建议

1. **降低日志级别**
   ```yaml
   logger:
     level: WARN  # 或 ERROR
     baud_rate: 0  # 禁用UART输出
   ```

2. **调整更新间隔**
   ```yaml
   sensor:
     - platform: adc
       update_interval: 300s  # 5分钟更新一次
   ```

3. **禁用不必要的组件**
   - 移除API组件（如果不需要）
   - 禁用状态LED
   - 减少BLE广播频率

## 故障排除

### 1. 编译错误
- 确保使用正确的board配置
- 检查Zephyr版本兼容性

### 2. BLE OTA连接失败
- 确认设备正在广播
- 检查设备名称是否正确
- 验证MCUboot是否正确配置

### 3. 电池监控不准确
- 调整ADC的multiply滤波器值
- 检查硬件电路的分压比

## 注意事项

1. **首次使用必须通过SWD烧录**
2. **MCUboot和DFU功能互斥**
3. **BLE beacon需要自定义实现**
4. **电池供电时注意功耗优化**

## 相关链接

- [ESPHome NRF52文档](https://esphome.io/components/nrf52.html)
- [MCUmgr Web工具](https://mcumgr-web.netlify.app/)
- [Zephyr MCUboot文档](https://docs.zephyrproject.org/latest/services/device_mgmt/mcumgr.html)
