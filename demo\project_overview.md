# NRF52833 Beacon Demo Project

## 项目概述

这是一个完整的NRF52833 BLE beacon项目，基于ESPHome框架开发，支持：
- 电池供电的低功耗运行
- BLE OTA无线固件更新
- 实时key数据BLE广播
- 自定义iBeacon实现

## 快速开始

### 1. 选择配置文件

项目提供了两个配置文件：

- **`nrf52833_demo.yaml`** - 基础版本，不包含自定义BLE beacon
- **`nrf52833_beacon_full.yaml`** - 完整版本，包含自定义BLE beacon组件

### 2. 编译和烧录

```bash
# 进入demo目录
cd demo

# 编译基础版本
esphome compile nrf52833_demo.yaml

# 或编译完整版本
esphome compile nrf52833_beacon_full.yaml

# 初次烧录（需要SWD调试器）
pyocd flash -t nrf52833 .esphome/build/nrf52833-beacon/zephyr/zephyr.hex
```

### 3. BLE OTA更新

1. 访问 https://mcumgr-web.netlify.app/
2. 连接到设备
3. 上传 `app_update.bin` 文件
4. 等待更新完成

## 文件说明

### 配置文件
- `nrf52833_demo.yaml` - 基础配置，适合初学者
- `nrf52833_beacon_full.yaml` - 完整配置，包含自定义组件

### 自定义组件
- `custom_components/nrf52_beacon/` - BLE beacon自定义组件
  - `__init__.py` - ESPHome组件配置
  - `nrf52_beacon.h` - C++头文件
  - `nrf52_beacon.cpp` - C++实现文件

### 文档
- `README.md` - 详细使用说明
- `project_overview.md` - 项目概览（本文件）

## 主要特性

### ✅ 已实现功能
- MCUboot bootloader配置
- BLE OTA固件更新支持
- 电池电压监控
- 模板传感器（key数据模拟）
- 状态LED指示
- 低功耗配置
- 自定义iBeacon组件框架

### 🔧 需要定制的部分
- 实际传感器替换模板传感器
- GPIO引脚配置调整
- 电池电压计算校准
- BLE广播数据格式定制

### ⚠️ 注意事项
- 首次烧录必须使用SWD
- MCUboot和Adafruit DFU互斥
- 自定义组件需要Zephyr BLE支持
- 电池供电时注意功耗优化

## 开发建议

### 1. 硬件适配
根据实际硬件修改以下配置：
```yaml
nrf52:
  board: your_actual_board  # 替换为实际板型

output:
  - platform: gpio
    pin: 
      number: XX  # 替换为实际LED引脚
```

### 2. 传感器集成
替换模板传感器：
```yaml
sensor:
  - platform: adc  # 或其他实际传感器
    pin: AIN0
    name: "Real Key Sensor"
    # ... 其他配置
```

### 3. 功耗优化
```yaml
logger:
  level: ERROR  # 降低日志级别
  baud_rate: 0  # 禁用串口输出

sensor:
  - platform: adc
    update_interval: 300s  # 延长更新间隔
```

## 故障排除

### 编译问题
1. 检查ESPHome版本兼容性
2. 确认NRF52平台支持
3. 验证自定义组件路径

### 烧录问题
1. 确认SWD连接正确
2. 检查目标芯片型号
3. 验证烧录器驱动

### BLE OTA问题
1. 确认MCUboot配置正确
2. 检查设备BLE广播状态
3. 验证mcumgr服务可用

## 扩展开发

### 添加新传感器
1. 在YAML中添加传感器配置
2. 修改BLE广播数据结构
3. 更新自定义组件代码

### 自定义BLE协议
1. 修改`nrf52_beacon.cpp`中的广播数据
2. 实现自定义数据编码
3. 添加数据解析逻辑

### 电源管理增强
1. 实现深度睡眠模式
2. 添加唤醒条件
3. 优化传感器采样策略

## 技术支持

如有问题，请检查：
1. ESPHome官方文档
2. Zephyr RTOS文档
3. NRF52系列芯片手册
4. MCUboot使用指南

---

**项目状态**: 开发中 🚧  
**最后更新**: 2024年  
**兼容性**: ESPHome 2024.x, Zephyr RTOS
