from .const import (
    VARIANT_ESP32,
    VARIANT_ESP32C2,
    VARIANT_ESP32C3,
    VARIANT_ESP32C5,
    VARIANT_ESP32C6,
    VARIANT_ESP32H2,
    VARIANT_ESP32P4,
    VARIANT_ESP32S2,
    VARIANT_ESP32S3,
    VARIANTS,
)

STANDARD_BOARDS = {
    VARIANT_ESP32: "esp32dev",
    VARIANT_ESP32C2: "esp32-c2-devkitm-1",
    VARIANT_ESP32C3: "esp32-c3-devkitm-1",
    VARIANT_ESP32C5: "esp32-c5-devkitc-1",
    VARIANT_ESP32C6: "esp32-c6-devkitm-1",
    VARIANT_ESP32H2: "esp32-h2-devkitm-1",
    VARIANT_ESP32P4: "esp32-p4-evboard",
    VARIANT_ESP32S2: "esp32-s2-kaluga-1",
    VARIANT_ESP32S3: "esp32-s3-devkitc-1",
}

# Make sure not missed here if a new variant added.
assert all(v in STANDARD_BOARDS for v in VARIANTS)

ESP32_BASE_PINS = {
    "TX": 1,
    "RX": 3,
    "SDA": 21,
    "SCL": 22,
    "SS": 5,
    "MOSI": 23,
    "MISO": 19,
    "SCK": 18,
    "A0": 36,
    "A3": 39,
    "A4": 32,
    "A5": 33,
    "A6": 34,
    "A7": 35,
    "A10": 4,
    "A11": 0,
    "A12": 2,
    "A13": 15,
    "A14": 13,
    "A15": 12,
    "A16": 14,
    "A17": 27,
    "A18": 25,
    "A19": 26,
    "T0": 4,
    "T1": 0,
    "T2": 2,
    "T3": 15,
    "T4": 13,
    "T5": 12,
    "T6": 14,
    "T7": 27,
    "T8": 33,
    "T9": 32,
    "DAC1": 25,
    "DAC2": 26,
    "SVP": 36,
    "SVN": 39,
}

ESP32_BOARD_PINS = {
    "adafruit_feather_esp32_v2": {
        "A0": 26,
        "A1": 25,
        "A2": 34,
        "A3": 39,
        "A4": 36,
        "A5": 4,
        "SCK": 5,
        "MOSI": 19,
        "MISO": 21,
        "RX": 7,
        "TX": 8,
        "D37": 37,
        "LED": 13,
        "LED_BUILTIN": 13,
        "D12": 12,
        "D27": 27,
        "D33": 33,
        "D15": 15,
        "D32": 32,
        "D14": 14,
        "SCL": 20,
        "SDA": 22,
        "BUTTON": 38,
        "NEOPIXEL": 0,
        "PIN_NEOPIXEL": 0,
        "NEOPIXEL_POWER": 2,
    },
    "adafruit_feather_esp32s2_tft": {
        "BUTTON": 0,
        "A0": 18,
        "A1": 17,
        "A2": 16,
        "A3": 15,
        "A4": 14,
        "A5": 8,
        "SCK": 36,
        "MOSI": 35,
        "MISO": 37,
        "RX": 2,
        "TX": 1,
        "D13": 13,
        "D12": 12,
        "D11": 11,
        "D10": 10,
        "D9": 9,
        "D6": 6,
        "D5": 5,
        "NEOPIXEL": 33,
        "PIN_NEOPIXEL": 33,
        "NEOPIXEL_POWER": 34,
        "SCL": 41,
        "SDA": 42,
        "TFT_I2C_POWER": 21,
        "TFT_CS": 7,
        "TFT_DC": 39,
        "TFT_RESET": 40,
        "TFT_BACKLIGHT": 45,
        "LED": 13,
        "LED_BUILTIN": 13,
    },
    "adafruit_feather_esp32s3": {
        "BUTTON": 0,
        "A0": 18,
        "A1": 17,
        "A2": 16,
        "A3": 15,
        "A4": 14,
        "A5": 8,
        "SCK": 36,
        "MOSI": 35,
        "MISO": 37,
        "RX": 38,
        "TX": 39,
        "SCL": 4,
        "SDA": 3,
        "NEOPIXEL": 33,
        "PIN_NEOPIXEL": 33,
        "NEOPIXEL_POWER": 21,
        "I2C_POWER": 7,
        "LED": 13,
        "LED_BUILTIN": 13,
    },
    "adafruit_feather_esp32s3_nopsram": {
        "BUTTON": 0,
        "A0": 18,
        "A1": 17,
        "A2": 16,
        "A3": 15,
        "A4": 14,
        "A5": 8,
        "SCK": 36,
        "MOSI": 35,
        "MISO": 37,
        "RX": 38,
        "TX": 39,
        "SCL": 4,
        "SDA": 3,
        "NEOPIXEL": 33,
        "PIN_NEOPIXEL": 33,
        "NEOPIXEL_POWER": 21,
        "I2C_POWER": 7,
        "LED": 13,
        "LED_BUILTIN": 13,
    },
    "adafruit_feather_esp32s3_tft": {
        "BUTTON": 0,
        "A0": 18,
        "A1": 17,
        "A2": 16,
        "A3": 15,
        "A4": 14,
        "A5": 8,
        "SCK": 36,
        "MOSI": 35,
        "MISO": 37,
        "RX": 2,
        "TX": 1,
        "SCL": 41,
        "SDA": 42,
        "NEOPIXEL": 33,
        "PIN_NEOPIXEL": 33,
        "NEOPIXEL_POWER": 34,
        "TFT_I2C_POWER": 21,
        "TFT_CS": 7,
        "TFT_DC": 39,
        "TFT_RESET": 40,
        "TFT_BACKLIGHT": 45,
        "LED": 13,
        "LED_BUILTIN": 13,
    },
    "adafruit_funhouse_esp32s2": {
        "BUTTON_UP": 5,
        "BUTTON_DOWN": 3,
        "BUTTON_SELECT": 4,
        "DOTSTAR_DATA": 14,
        "DOTSTAR_CLOCK": 15,
        "PIR_SENSE": 16,
        "A0": 17,
        "A1": 2,
        "A2": 1,
        "CAP6": 6,
        "CAP7": 7,
        "CAP8": 8,
        "CAP9": 9,
        "CAP10": 10,
        "CAP11": 11,
        "CAP12": 12,
        "CAP13": 13,
        "SPEAKER": 42,
        "LED": 37,
        "LIGHT": 18,
        "TFT_MOSI": 35,
        "TFT_SCK": 36,
        "TFT_CS": 40,
        "TFT_DC": 39,
        "TFT_RESET": 41,
        "TFT_BACKLIGHT": 21,
        "RED_LED": 31,
        "BUTTON": 0,
    },
    "adafruit_itsybitsy_esp32": {
        "A0": 25,
        "A1": 26,
        "A2": 4,
        "A3": 38,
        "A4": 37,
        "A5": 36,
        "SCK": 19,
        "MOSI": 21,
        "MISO": 22,
        "SCL": 27,
        "SDA": 15,
        "TX": 20,
        "RX": 8,
        "NEOPIXEL": 0,
        "PIN_NEOPIXEL": 0,
        "NEOPIXEL_POWER": 2,
        "BUTTON": 35,
    },
    "adafruit_magtag29_esp32s2": {
        "A1": 18,
        "BUTTON_A": 15,
        "BUTTON_B": 14,
        "BUTTON_C": 12,
        "BUTTON_D": 11,
        "SDA": 33,
        "SCL": 34,
        "SPEAKER": 17,
        "SPEAKER_ENABLE": 16,
        "VOLTAGE_MONITOR": 4,
        "ACCELEROMETER_INT": 9,
        "ACCELEROMETER_INTERRUPT": 9,
        "LIGHT": 3,
        "NEOPIXEL": 1,
        "PIN_NEOPIXEL": 1,
        "NEOPIXEL_POWER": 21,
        "EPD_BUSY": 5,
        "EPD_RESET": 6,
        "EPD_DC": 7,
        "EPD_CS": 8,
        "EPD_MOSI": 35,
        "EPD_SCK": 36,
        "EPD_MISO": 37,
        "BUTTON": 0,
        "LED": 13,
        "LED_BUILTIN": 13,
    },
    "adafruit_metro_esp32s2": {
        "A0": 17,
        "A1": 18,
        "A2": 1,
        "A3": 2,
        "A4": 3,
        "A5": 4,
        "RX": 38,
        "TX": 37,
        "SCL": 34,
        "SDA": 33,
        "MISO": 37,
        "SCK": 36,
        "MOSI": 35,
        "NEOPIXEL": 45,
        "PIN_NEOPIXEL": 45,
        "LED": 42,
        "LED_BUILTIN": 42,
        "BUTTON": 0,
    },
    "adafruit_qtpy_esp32c3": {
        "A0": 4,
        "A1": 3,
        "A2": 1,
        "A3": 0,
        "SDA": 5,
        "SCL": 6,
        "MOSI": 7,
        "MISO": 8,
        "SCK": 10,
        "RX": 20,
        "TX": 21,
        "NEOPIXEL": 2,
        "PIN_NEOPIXEL": 2,
        "BUTTON": 9,
        "SWITCH": 9,
    },
    "adafruit_qtpy_esp32s2": {
        "A0": 18,
        "A1": 17,
        "A2": 9,
        "A3": 8,
        "SDA": 7,
        "SCL": 6,
        "MOSI": 35,
        "MISO": 37,
        "SCK": 36,
        "RX": 16,
        "TX": 5,
        "SDA1": 41,
        "SCL1": 40,
        "NEOPIXEL": 39,
        "PIN_NEOPIXEL": 39,
        "NEOPIXEL_POWER": 38,
        "D0": 0,
        "BUTTON": 0,
        "SWITCH": 0,
    },
    "adafruit_qtpy_esp32s3_nopsram": {
        "A0": 18,
        "A1": 17,
        "A2": 9,
        "A3": 8,
        "SDA": 7,
        "SCL": 6,
        "MOSI": 35,
        "MISO": 37,
        "SCK": 36,
        "RX": 16,
        "TX": 5,
        "SDA1": 41,
        "SCL1": 40,
        "NEOPIXEL": 39,
        "PIN_NEOPIXEL": 39,
        "NEOPIXEL_POWER": 38,
        "BUTTON": 0,
        "SWITCH": 0,
    },
    "adafruit_qtpy_esp32": {
        "A0": 26,
        "A1": 25,
        "A2": 27,
        "A3": 15,
        "SDA": 4,
        "SCL": 33,
        "MOSI": 13,
        "MISO": 12,
        "SCK": 14,
        "RX": 7,
        "TX": 32,
        "SDA1": 22,
        "SCL1": 19,
        "NEOPIXEL": 5,
        "PIN_NEOPIXEL": 5,
        "NEOPIXEL_POWER": 8,
        "BUTTON": 0,
        "SWITCH": 0,
    },
    "airm2m_core_esp32c3": {
        "LED1_BUILTIN": 12,
        "LED2_BUILTIN": 13,
    },
    "alksesp32": {
        "A0": 32,
        "A1": 33,
        "A2": 25,
        "A3": 26,
        "A4": 27,
        "A5": 14,
        "A6": 12,
        "A7": 15,
        "D0": 40,
        "D1": 41,
        "D10": 19,
        "D11": 21,
        "D12": 22,
        "D13": 23,
        "D2": 15,
        "D3": 2,
        "D4": 0,
        "D5": 4,
        "D6": 16,
        "D7": 17,
        "D8": 5,
        "D9": 18,
        "DHT_PIN": 26,
        "LED": 23,
        "L_B": 5,
        "L_G": 17,
        "L_R": 22,
        "L_RGB_B": 16,
        "L_RGB_G": 21,
        "L_RGB_R": 4,
        "L_Y": 23,
        "MISO": 22,
        "MOSI": 21,
        "PHOTO": 25,
        "PIEZO1": 19,
        "PIEZO2": 18,
        "POT1": 32,
        "POT2": 33,
        "S1": 4,
        "S2": 16,
        "S3": 18,
        "S4": 19,
        "S5": 21,
        "SCK": 23,
        "SCL": 14,
        "SDA": 27,
        "SS": 19,
        "SW1": 15,
        "SW2": 2,
        "SW3": 0,
    },
    "az-delivery-devkit-v4": {},
    "bpi-bit": {
        "BUTTON_A": 35,
        "BUTTON_B": 27,
        "BUZZER": 25,
        "LIGHT_SENSOR1": 36,
        "LIGHT_SENSOR2": 39,
        "MPU9250_INT": 0,
        "P0": 25,
        "P1": 32,
        "P10": 26,
        "P11": 27,
        "P12": 2,
        "P13": 18,
        "P14": 19,
        "P15": 23,
        "P16": 5,
        "P19": 22,
        "P2": 33,
        "P20": 21,
        "P3": 13,
        "P4": 15,
        "P5": 35,
        "P6": 12,
        "P7": 14,
        "P8": 16,
        "P9": 17,
        "RGB_LED": 4,
        "TEMPERATURE_SENSOR": 34,
    },
    "briki_abc_esp32": {},
    "briki_mbc-wb_esp32": {},
    "d-duino-32": {
        "D1": 5,
        "D10": 1,
        "D2": 4,
        "D3": 0,
        "D4": 2,
        "D5": 14,
        "D6": 12,
        "D7": 13,
        "D8": 15,
        "D9": 3,
        "MISO": 12,
        "MOSI": 13,
        "SCK": 14,
        "SCL": 4,
        "SDA": 5,
        "SS": 15,
    },
    "denky_d4": {"RX": 8, "LED": 14},
    "esp-wrover-kit": {},
    "esp32-devkitlipo": {},
    "esp32-evb": {
        "BUTTON": 34,
        "MISO": 15,
        "MOSI": 2,
        "SCK": 14,
        "SCL": 16,
        "SDA": 13,
        "SS": 17,
    },
    "esp32-gateway": {"BUTTON": 34, "LED": 33, "SCL": 16, "SDA": 32},
    "esp32-poe-iso": {
        "BUTTON": 34,
        "MISO": 15,
        "MOSI": 2,
        "SCK": 14,
        "SCL": 16,
        "SDA": 13,
    },
    "esp32-poe": {"BUTTON": 34, "MISO": 15, "MOSI": 2, "SCK": 14, "SCL": 16, "SDA": 13},
    "esp32-pro": {
        "BUTTON": 34,
        "MISO": 15,
        "MOSI": 2,
        "SCK": 14,
        "SCL": 16,
        "SDA": 13,
        "SS": 17,
    },
    "esp320": {
        "LED": 5,
        "MISO": 12,
        "MOSI": 13,
        "SCK": 14,
        "SCL": 14,
        "SDA": 2,
        "SS": 15,
    },
    "esp32cam": {},
    "esp32dev": {},
    "esp32doit-devkit-v1": {"LED": 2},
    "esp32doit-espduino": {"TX0": 1, "RX0": 3, "CMD": 11, "CLK": 6, "SD0": 7, "SD1": 8},
    "esp32thing": {"BUTTON": 0, "LED": 5, "SS": 2},
    "esp32thing_plus": {
        "SDA": 23,
        "SCL": 22,
        "SS": 33,
        "MOSI": 18,
        "MISO": 19,
        "SCK": 5,
        "A0": 26,
        "A1": 25,
        "A2": 34,
        "A3": 39,
        "A4": 36,
        "A5": 4,
        "A6": 14,
        "A7": 32,
        "A8": 15,
        "A9": 33,
        "A10": 27,
        "A11": 12,
        "A12": 13,
    },
    "esp32vn-iot-uno": {},
    "espea32": {"BUTTON": 0, "LED": 5},
    "espectro32": {"LED": 15, "SD_SS": 33},
    "espino32": {"BUTTON": 0, "LED": 16},
    "etboard": {
        "LED_BUILTIN": 5,
        "TX": 34,
        "RX": 35,
        "SS": 29,
        "MOSI": 37,
        "MISO": 31,
        "SCK": 30,
        "A0": 36,
        "A1": 39,
        "A2": 32,
        "A3": 33,
        "A4": 34,
        "A5": 35,
        "A6": 25,
        "A7": 26,
        "D2": 27,
        "D3": 14,
        "D4": 12,
        "D5": 13,
        "D6": 15,
        "D7": 16,
        "D8": 17,
        "D9": 4,
    },
    "featheresp32": {
        "A0": 26,
        "A1": 25,
        "A10": 27,
        "A11": 12,
        "A12": 13,
        "A13": 35,
        "A2": 34,
        "A4": 36,
        "A5": 4,
        "A6": 14,
        "A7": 32,
        "A8": 15,
        "A9": 33,
        "Ax": 2,
        "LED": 13,
        "MOSI": 18,
        "RX": 16,
        "SCK": 5,
        "SDA": 23,
        "SS": 33,
        "TX": 17,
    },
    "featheresp32-s2": {
        "SDA": 3,
        "SCL": 4,
        "SS": 42,
        "MOSI": 35,
        "SCK": 36,
        "MISO": 37,
        "A0": 18,
        "A1": 17,
        "A10": 27,
        "A11": 12,
        "A12": 13,
        "A13": 35,
        "A2": 16,
        "A3": 15,
        "A4": 14,
        "A5": 8,
        "LED": 13,
        "TX": 39,
        "RX": 38,
        "T5": 5,
        "T8": 8,
        "T9": 9,
        "T10": 10,
        "T11": 11,
        "T12": 12,
        "T13": 13,
        "T14": 14,
        "DAC1": 17,
        "DAC2": 18,
    },
    "firebeetle32": {"LED": 2},
    "fm-devkit": {
        "D0": 34,
        "D1": 35,
        "D10": 0,
        "D2": 32,
        "D3": 33,
        "D4": 27,
        "D5": 14,
        "D6": 12,
        "D7": 13,
        "D8": 15,
        "D9": 23,
        "I2S_DOUT": 22,
        "I2S_LRCLK": 25,
        "I2S_MCLK": 2,
        "I2S_SCLK": 26,
        "LED": 5,
        "SCL": 17,
        "SDA": 16,
        "SW1": 4,
        "SW2": 18,
        "SW3": 19,
        "SW4": 21,
    },
    "frogboard": {},
    "healtypi4": {
        "KEY_BUILTIN": 17,
        "ADS1292_DRDY_PIN": 26,
        "ADS1292_CS_PIN": 13,
        "ADS1292_START_PIN": 14,
        "ADS1292_PWDN_PIN": 27,
        "AFE4490_CS_PIN": 21,
        "AFE4490_DRDY_PIN": 39,
        "AFE4490_PWDN_PIN": 4,
        "PUSH_BUTTON": 17,
        "SLIDE_SWITCH": 16,
    },
    "heltec_wifi_kit_32": {
        "A1": 37,
        "A2": 38,
        "BUTTON": 0,
        "LED": 25,
        "RST_OLED": 16,
        "SCL_OLED": 15,
        "SDA_OLED": 4,
        "Vext": 21,
    },
    "heltec_wifi_kit_32_v2": "heltec_wifi_kit_32",
    "heltec_wifi_lora_32": {
        "BUTTON": 0,
        "DIO0": 26,
        "DIO1": 33,
        "DIO2": 32,
        "LED": 25,
        "MOSI": 27,
        "RST_LoRa": 14,
        "RST_OLED": 16,
        "SCK": 5,
        "SCL_OLED": 15,
        "SDA_OLED": 4,
        "SS": 18,
        "Vext": 21,
    },
    "heltec_wifi_lora_32_V2": {
        "BUTTON": 0,
        "DIO0": 26,
        "DIO1": 35,
        "DIO2": 34,
        "LED": 25,
        "MOSI": 27,
        "RST_LoRa": 14,
        "RST_OLED": 16,
        "SCK": 5,
        "SCL_OLED": 15,
        "SDA_OLED": 4,
        "SS": 18,
        "Vext": 21,
    },
    "heltec_wireless_stick": {
        "BUTTON": 0,
        "DIO0": 26,
        "DIO1": 35,
        "DIO2": 34,
        "LED": 25,
        "MOSI": 27,
        "RST_LoRa": 14,
        "RST_OLED": 16,
        "SCK": 5,
        "SCL_OLED": 15,
        "SDA_OLED": 4,
        "SS": 18,
        "Vext": 21,
    },
    "heltec_wireless_stick_lite": {
        "LED_BUILTIN": 25,
        "KEY_BUILTIN": 0,
        "SS": 18,
        "MOSI": 27,
        "MISO": 19,
        "SCK": 5,
        "Vext": 21,
        "LED": 25,
        "RST_LoRa": 14,
        "DIO0": 26,
        "DIO1": 35,
        "DIO2": 34,
    },
    "honeylemon": {
        "LED_BUILTIN": 2,
        "BUILTIN_KEY": 0,
    },
    "hornbill32dev": {"BUTTON": 0, "LED": 13},
    "hornbill32minima": {"SS": 2},
    "imbrios-logsens-v1p1": {
        "LED_BUILTIN": 33,
        "UART2_TX": 17,
        "UART2_RX": 16,
        "UART2_RTS": 4,
        "CAN_TX": 17,
        "CAN_RX": 16,
        "CAN_TXDE": 4,
        "SS": 15,
        "MOSI": 13,
        "MISO": 12,
        "SCK": 14,
        "SPI_SS1": 23,
        "BUZZER_CTRL": 19,
        "SD_CARD_DETECT": 35,
        "SW2_BUILDIN": 0,
        "SW3_BUILDIN": 36,
        "SW4_BUILDIN": 34,
        "LED1_BUILDIN": 32,
        "LED2_BUILDIN": 33,
    },
    "inex_openkb": {
        "LED_BUILTIN": 16,
        "LDR_PIN": 36,
        "SW1": 16,
        "SW2": 14,
        "BT_LED": 17,
        "WIFI_LED": 2,
        "NTP_LED": 15,
        "IOT_LED": 12,
        "BUZZER": 13,
        "INPUT1": 32,
        "INPUT2": 33,
        "INPUT3": 34,
        "INPUT4": 35,
        "OUTPUT1": 26,
        "OUTPUT2": 27,
        "SDA0": 21,
        "SCL0": 22,
        "SDA1": 4,
        "SCL1": 5,
    },
    "intorobot": {
        "A1": 39,
        "A2": 35,
        "A3": 25,
        "A4": 26,
        "A5": 14,
        "A6": 12,
        "A7": 15,
        "A8": 13,
        "A9": 2,
        "BUTTON": 0,
        "D0": 19,
        "D1": 23,
        "D2": 18,
        "D3": 17,
        "D4": 16,
        "D5": 5,
        "D6": 4,
        "LED": 4,
        "MISO": 17,
        "MOSI": 16,
        "RGB_B_BUILTIN": 22,
        "RGB_G_BUILTIN": 21,
        "RGB_R_BUILTIN": 27,
        "SCL": 19,
        "SDA": 23,
        "T0": 19,
        "T1": 23,
        "T2": 18,
        "T3": 17,
        "T4": 16,
        "T5": 5,
        "T6": 4,
    },
    "iotaap_magnolia": {},
    "iotbusio": {},
    "iotbusproteus": {},
    "kits-edu": {},
    "labplus_mpython": {
        "SDA": 23,
        "SCL": 22,
        "P0": 33,
        "P1": 32,
        "P2": 35,
        "P3": 34,
        "P4": 39,
        "P5": 0,
        "P6": 16,
        "P7": 17,
        "P8": 26,
        "P9": 25,
        "P10": 36,
        "P11": 2,
        "P13": 18,
        "P14": 19,
        "P15": 21,
        "P16": 5,
        "P19": 22,
        "P20": 23,
        "P": 27,
        "Y": 14,
        "T": 12,
        "H": 13,
        "O": 15,
        "N": 4,
        "BTN_A": 0,
        "BTN_B": 2,
        "SOUND": 36,
        "LIGHT": 39,
        "BUZZER": 16,
    },
    "lolin32": {"LED": 5},
    "lolin32_lite": {"LED": 22},
    "lolin_c3_mini": {
        "TX": 21,
        "RX": 20,
        "SDA": 8,
        "SCL": 10,
        "SS": 5,
        "MOSI": 4,
        "MISO": 3,
        "SCK": 2,
        "A0": 0,
        "A1": 1,
        "A2": 2,
        "A3": 3,
        "A4": 4,
        "A5": 5,
        "D0": 1,
        "D1": 10,
        "D2": 8,
        "D3": 7,
        "D4": 6,
        "D5": 2,
        "D6": 3,
        "D7": 4,
        "D8": 5,
        "LED": 7,
        "BUTTON": 9,
    },
    "lolin_d32": {"LED": 5, "_VBAT": 35},
    "lolin_d32_pro": {"LED": 5, "_VBAT": 35},
    "lolin_s2_mini": {
        "TX": 43,
        "RX": 44,
        "SPICS1": 29,
        "SPIHD": 31,
        "SPIWP": 32,
        "SPICS0": 33,
        "SPICLK": 34,
        "SPIQ": 35,
        "SPID": 36,
        "MISO": 9,
        "MOSI": 11,
        "SCK": 7,
        "SCL": 35,
        "SDA": 33,
        "DAC1": 17,
        "DAC2": 18,
        "LED": 15,
    },
    "lopy": {
        "A1": 37,
        "A2": 38,
        "LED": 0,
        "MISO": 37,
        "MOSI": 22,
        "SCK": 13,
        "SCL": 13,
        "SDA": 12,
        "SS": 17,
    },
    "lopy4": {
        "A1": 37,
        "A2": 38,
        "LED": 0,
        "MISO": 37,
        "MOSI": 22,
        "SCK": 13,
        "SCL": 13,
        "SDA": 12,
        "SS": 18,
    },
    "m5stack-atom": {
        "SDA": 26,
        "SCL": 32,
        "ADC1": 35,
        "ADC2": 36,
        "SS": 19,
        "MOSI": 33,
        "MISO": 23,
        "SCK": 22,
    },
    "m5stack-core-esp32": {
        "ADC1": 35,
        "ADC2": 36,
        "G0": 0,
        "G1": 1,
        "G12": 12,
        "G13": 13,
        "G15": 15,
        "G16": 16,
        "G17": 17,
        "G18": 18,
        "G19": 19,
        "G2": 2,
        "G21": 21,
        "G22": 22,
        "G23": 23,
        "G25": 25,
        "G26": 26,
        "G3": 3,
        "G34": 34,
        "G35": 35,
        "G36": 36,
        "G5": 5,
        "RXD2": 16,
        "TXD2": 17,
    },
    "m5stack-core2": {
        "SDA": 32,
        "SCL": 33,
        "SS": 5,
        "MOSI": 23,
        "MISO": 38,
        "SCK": 18,
        "ADC1": 35,
        "ADC2": 36,
    },
    "m5stack-coreink": {
        "SDA": 32,
        "SCL": 33,
        "SS": 9,
        "MOSI": 23,
        "MISO": 34,
        "SCK": 18,
        "ADC1": 35,
        "ADC2": 36,
    },
    "m5stack-fire": {
        "ADC1": 35,
        "ADC2": 36,
        "G0": 0,
        "G1": 1,
        "G12": 12,
        "G13": 13,
        "G15": 15,
        "G16": 16,
        "G17": 17,
        "G18": 18,
        "G19": 19,
        "G2": 2,
        "G21": 21,
        "G22": 22,
        "G23": 23,
        "G25": 25,
        "G26": 26,
        "G3": 3,
        "G34": 34,
        "G35": 35,
        "G36": 36,
        "G5": 5,
    },
    "m5stack-grey": {
        "ADC1": 35,
        "ADC2": 36,
        "G0": 0,
        "G1": 1,
        "G12": 12,
        "G13": 13,
        "G15": 15,
        "G16": 16,
        "G17": 17,
        "G18": 18,
        "G19": 19,
        "G2": 2,
        "G21": 21,
        "G22": 22,
        "G23": 23,
        "G25": 25,
        "G26": 26,
        "G3": 3,
        "G34": 34,
        "G35": 35,
        "G36": 36,
        "G5": 5,
        "RXD2": 16,
        "TXD2": 17,
    },
    "m5stack-timer-cam": {
        "LED_BUILTIN": 2,
        "SDA": 4,
        "SCL": 13,
        "SS": 5,
        "MOSI": 23,
        "MISO": 19,
        "SCK": 18,
        "ADC1": 35,
        "ADC2": 36,
    },
    "m5stick-c": {
        "ADC1": 35,
        "ADC2": 36,
        "G0": 0,
        "G10": 10,
        "G26": 26,
        "G32": 32,
        "G33": 33,
        "G36": 36,
        "G37": 37,
        "G39": 39,
        "G9": 9,
        "MISO": 36,
        "MOSI": 15,
        "SCK": 13,
        "SCL": 33,
        "SDA": 32,
    },
    "magicbit": {
        "BLUE_LED": 17,
        "BUZZER": 25,
        "GREEN_LED": 16,
        "LDR": 36,
        "LED": 16,
        "LEFT_BUTTON": 35,
        "MOTOR1A": 27,
        "MOTOR1B": 18,
        "MOTOR2A": 16,
        "MOTOR2B": 17,
        "POT": 39,
        "RED_LED": 27,
        "RIGHT_PUTTON": 34,
        "YELLOW_LED": 18,
    },
    "mgbot-iotik32a": {
        "LED_BUILTIN": 4,
        "TX2": 17,
        "RX2": 16,
    },
    "mgbot-iotik32b": {
        "LED_BUILTIN": 18,
        "IR": 27,
        "TX2": 17,
        "RX2": 16,
    },
    "mhetesp32devkit": {"LED": 2},
    "mhetesp32minikit": {"LED": 2},
    "microduino-core-esp32": {
        "A0": 12,
        "A1": 13,
        "A10": 25,
        "A11": 26,
        "A12": 27,
        "A13": 14,
        "A2": 15,
        "A3": 4,
        "A6": 38,
        "A7": 37,
        "A8": 32,
        "A9": 33,
        "D0": 3,
        "D1": 1,
        "D10": 5,
        "D11": 23,
        "D12": 19,
        "D13": 18,
        "D14": 12,
        "D15": 13,
        "D16": 15,
        "D17": 4,
        "D18": 22,
        "D19": 21,
        "D2": 16,
        "D20": 38,
        "D21": 37,
        "D3": 17,
        "D4": 32,
        "D5": 33,
        "D6": 25,
        "D7": 26,
        "D8": 27,
        "D9": 14,
        "SCL": 21,
        "SCL1": 13,
        "SDA": 22,
        "SDA1": 12,
    },
    "nano32": {"BUTTON": 0, "LED": 16},
    "nina_w10": {
        "D0": 3,
        "D1": 1,
        "D10": 5,
        "D11": 19,
        "D12": 23,
        "D13": 18,
        "D14": 13,
        "D15": 12,
        "D16": 32,
        "D17": 33,
        "D18": 21,
        "D19": 34,
        "D2": 26,
        "D20": 36,
        "D21": 39,
        "D3": 25,
        "D4": 35,
        "D5": 27,
        "D6": 22,
        "D7": 0,
        "D8": 15,
        "D9": 14,
        "LED_BLUE": 21,
        "LED_GREEN": 33,
        "LED_RED": 23,
        "SCL": 13,
        "SDA": 12,
        "SW1": 33,
        "SW2": 27,
    },
    "node32s": {},
    "nodemcu-32s": {"BUTTON": 0, "LED": 2},
    "nscreen-32": {},
    "odroid_esp32": {"ADC1": 35, "ADC2": 36, "LED": 2, "SCL": 4, "SDA": 15, "SS": 22},
    "onehorse32dev": {"A1": 37, "A2": 38, "BUTTON": 0, "LED": 5},
    "oroca_edubot": {
        "A0": 34,
        "A1": 39,
        "A2": 36,
        "A3": 33,
        "D0": 4,
        "D1": 16,
        "D2": 17,
        "D3": 22,
        "D4": 23,
        "D5": 5,
        "D6": 18,
        "D7": 19,
        "D8": 33,
        "LED": 13,
        "MOSI": 18,
        "RX": 16,
        "SCK": 5,
        "SDA": 23,
        "SS": 2,
        "TX": 17,
        "VBAT": 35,
    },
    "pico32": {},
    "piranha_esp32": {
        "LED_BUILTIN": 2,
        "KEY_BUILTIN": 0,
    },
    "pocket_32": {"LED": 16},
    "pycom_gpy": {
        "A1": 37,
        "A2": 38,
        "LED": 0,
        "MISO": 37,
        "MOSI": 22,
        "SCK": 13,
        "SCL": 13,
        "SDA": 12,
        "SS": 17,
    },
    "qchip": "heltec_wifi_kit_32",
    "quantum": {},
    "s_odi_ultra": {
        "LED_BUILTIN": 2,
        "LED_BUILTINB": 4,
    },
    "sensesiot_weizen": {},
    "sg-o_airMon": {},
    "sparkfun_lora_gateway_1-channel": {"MISO": 12, "MOSI": 13, "SCK": 14, "SS": 16},
    "tinypico": {},
    "ttgo-lora32-v1": {
        "A1": 37,
        "A2": 38,
        "BUTTON": 0,
        "LED": 2,
        "MOSI": 27,
        "SCK": 5,
        "SS": 18,
    },
    "ttgo-lora32-v2": {
        "LED_BUILTIN": 22,
        "KEY_BUILTIN": 0,
        "SS": 18,
        "MOSI": 27,
        "MISO": 19,
        "SCK": 5,
        "A1": 37,
        "A2": 38,
    },
    "ttgo-lora32-v21": {
        "LED_BUILTIN": 25,
        "KEY_BUILTIN": 0,
        "SS": 18,
        "MOSI": 27,
        "MISO": 19,
        "SCK": 5,
        "A1": 37,
        "A2": 38,
    },
    "ttgo-t-beam": {"BUTTON": 39, "LED": 14, "MOSI": 27, "SCK": 5, "SS": 18},
    "ttgo-t-watch": {"BUTTON": 36, "MISO": 2, "MOSI": 15, "SCK": 14, "SS": 13},
    "ttgo-t1": {"LED": 22, "MISO": 2, "MOSI": 15, "SCK": 14, "SCL": 23, "SS": 13},
    "ttgo-t7-v13-mini32": {"LED": 22},
    "ttgo-t7-v14-mini32": {"LED": 19},
    "turta_iot_node": {},
    "vintlabs-devkit-v1": {
        "LED": 2,
        "PWM0": 12,
        "PWM1": 13,
        "PWM2": 14,
        "PWM3": 15,
        "PWM4": 16,
        "PWM5": 17,
        "PWM6": 18,
        "PWM7": 19,
    },
    "wemos_d1_mini32": {
        "D0": 26,
        "D1": 22,
        "D2": 21,
        "D3": 17,
        "D4": 16,
        "D5": 18,
        "D6": 19,
        "D7": 23,
        "D8": 5,
        "LED": 2,
        "RXD": 3,
        "TXD": 1,
        "_VBAT": 35,
    },
    "wemosbat": {"LED": 16},
    "wesp32": {
        "MISO": 32,
        "MOSI": 23,
        "SCK": 18,
        "SCL": 4,
        "SDA": 15,
        "MISO1": 12,
        "MOSI1": 13,
        "SCK1": 14,
        "SCL1": 5,
        "SDA1": 33,
    },
    "widora-air": {
        "A1": 39,
        "A2": 35,
        "A3": 25,
        "A4": 26,
        "A5": 14,
        "A6": 12,
        "A7": 15,
        "A8": 13,
        "A9": 2,
        "BUTTON": 0,
        "D0": 19,
        "D1": 23,
        "D2": 18,
        "D3": 17,
        "D4": 16,
        "D5": 5,
        "D6": 4,
        "LED": 25,
        "MISO": 17,
        "MOSI": 16,
        "SCL": 19,
        "SDA": 23,
        "T0": 19,
        "T1": 23,
        "T2": 18,
        "T3": 17,
        "T4": 16,
        "T5": 5,
        "T6": 4,
    },
    "wifiduino32": {
        "LED_BUILTIN": 2,
        "KEY_BUILTIN": 0,
        "SDA": 5,
        "SCL": 16,
        "A0": 27,
        "A1": 14,
        "A2": 12,
        "A3": 35,
        "A4": 13,
        "A5": 4,
        "D0": 3,
        "D1": 1,
        "D2": 17,
        "D3": 15,
        "D4": 32,
        "D5": 33,
        "D6": 25,
        "D7": 26,
        "D8": 23,
        "D9": 22,
        "D10": 21,
        "D11": 19,
        "D12": 18,
        "D13": 2,
    },
    "xinabox_cw02": {"LED": 27},
    "upesy_wroom": {"LED": 2},
    "upesy_wrover": {"LED": 2},
}

"""
BOARDS generated with script/generate-esp32-boards.py
"""

BOARDS = {
    "4d_systems_esp32s3_gen4_r8n16": {
        "name": "4D Systems GEN4-ESP32 16MB (ESP32S3-R8N16)",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_camera_esp32s3": {
        "name": "Adafruit pyCamera S3",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_feather_esp32_v2": {
        "name": "Adafruit Feather ESP32 V2",
        "variant": VARIANT_ESP32,
    },
    "adafruit_feather_esp32c6": {
        "name": "Adafruit Feather ESP32-C6",
        "variant": VARIANT_ESP32C6,
    },
    "adafruit_feather_esp32s2": {
        "name": "Adafruit Feather ESP32-S2",
        "variant": VARIANT_ESP32S2,
    },
    "adafruit_feather_esp32s2_reversetft": {
        "name": "Adafruit Feather ESP32-S2 Reverse TFT",
        "variant": VARIANT_ESP32S2,
    },
    "adafruit_feather_esp32s2_tft": {
        "name": "Adafruit Feather ESP32-S2 TFT",
        "variant": VARIANT_ESP32S2,
    },
    "adafruit_feather_esp32s3": {
        "name": "Adafruit Feather ESP32-S3 2MB PSRAM",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_feather_esp32s3_nopsram": {
        "name": "Adafruit Feather ESP32-S3 No PSRAM",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_feather_esp32s3_reversetft": {
        "name": "Adafruit Feather ESP32-S3 Reverse TFT",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_feather_esp32s3_tft": {
        "name": "Adafruit Feather ESP32-S3 TFT",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_funhouse_esp32s2": {
        "name": "Adafruit FunHouse",
        "variant": VARIANT_ESP32S2,
    },
    "adafruit_itsybitsy_esp32": {
        "name": "Adafruit ItsyBitsy ESP32",
        "variant": VARIANT_ESP32,
    },
    "adafruit_magtag29_esp32s2": {
        "name": "Adafruit MagTag 2.9",
        "variant": VARIANT_ESP32S2,
    },
    "adafruit_matrixportal_esp32s3": {
        "name": "Adafruit MatrixPortal ESP32-S3",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_metro_esp32s2": {
        "name": "Adafruit Metro ESP32-S2",
        "variant": VARIANT_ESP32S2,
    },
    "adafruit_metro_esp32s3": {
        "name": "Adafruit Metro ESP32-S3",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_qtpy_esp32": {
        "name": "Adafruit QT Py ESP32",
        "variant": VARIANT_ESP32,
    },
    "adafruit_qtpy_esp32c3": {
        "name": "Adafruit QT Py ESP32-C3",
        "variant": VARIANT_ESP32C3,
    },
    "adafruit_qtpy_esp32s2": {
        "name": "Adafruit QT Py ESP32-S2",
        "variant": VARIANT_ESP32S2,
    },
    "adafruit_qtpy_esp32s3_n4r2": {
        "name": "Adafruit QT Py ESP32-S3 (4M Flash 2M PSRAM)",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_qtpy_esp32s3_nopsram": {
        "name": "Adafruit QT Py ESP32-S3 No PSRAM",
        "variant": VARIANT_ESP32S3,
    },
    "adafruit_qualia_s3_rgb666": {
        "name": "Adafruit Qualia ESP32-S3 RGB666",
        "variant": VARIANT_ESP32S3,
    },
    "airm2m_core_esp32c3": {
        "name": "AirM2M CORE ESP32C3",
        "variant": VARIANT_ESP32C3,
    },
    "alksesp32": {
        "name": "ALKS ESP32",
        "variant": VARIANT_ESP32,
    },
    "arduino_nano_esp32": {
        "name": "Arduino Nano ESP32",
        "variant": VARIANT_ESP32S3,
    },
    "atd147_s3": {
        "name": "ArtronShop ATD1.47-S3",
        "variant": VARIANT_ESP32S3,
    },
    "atmegazero_esp32s2": {
        "name": "EspinalLab ATMegaZero ESP32-S2",
        "variant": VARIANT_ESP32S2,
    },
    "aventen_s3_sync": {
        "name": "Aventen S3 Sync",
        "variant": VARIANT_ESP32S3,
    },
    "az-delivery-devkit-v4": {
        "name": "AZ-Delivery ESP-32 Dev Kit C V4",
        "variant": VARIANT_ESP32,
    },
    "bee_data_logger": {
        "name": "Smart Bee Data Logger",
        "variant": VARIANT_ESP32S3,
    },
    "bee_motion": {
        "name": "Smart Bee Motion",
        "variant": VARIANT_ESP32S2,
    },
    "bee_motion_mini": {
        "name": "Smart Bee Motion Mini",
        "variant": VARIANT_ESP32C3,
    },
    "bee_motion_s3": {
        "name": "Smart Bee Motion S3",
        "variant": VARIANT_ESP32S3,
    },
    "bee_s3": {
        "name": "Smart Bee S3",
        "variant": VARIANT_ESP32S3,
    },
    "bpi-bit": {
        "name": "BPI-Bit",
        "variant": VARIANT_ESP32,
    },
    "bpi_leaf_s3": {
        "name": "BPI-Leaf-S3",
        "variant": VARIANT_ESP32S3,
    },
    "cnrs_aw2eth": {
        "name": "CNRS AW2ETH",
        "variant": VARIANT_ESP32,
    },
    "connaxio_espoir": {
        "name": "Connaxio's Espoir",
        "variant": VARIANT_ESP32,
    },
    "cytron_maker_feather_aiot_s3": {
        "name": "Cytron Maker Feather AIoT S3",
        "variant": VARIANT_ESP32S3,
    },
    "d-duino-32": {
        "name": "D-duino-32",
        "variant": VARIANT_ESP32,
    },
    "deneyapkart": {
        "name": "Deneyap Kart",
        "variant": VARIANT_ESP32,
    },
    "deneyapkart1A": {
        "name": "Deneyap Kart 1A",
        "variant": VARIANT_ESP32,
    },
    "deneyapkart1Av2": {
        "name": "Deneyap Kart 1A v2",
        "variant": VARIANT_ESP32S3,
    },
    "deneyapkartg": {
        "name": "Deneyap Kart G",
        "variant": VARIANT_ESP32C3,
    },
    "deneyapmini": {
        "name": "Deneyap Mini",
        "variant": VARIANT_ESP32S2,
    },
    "deneyapminiv2": {
        "name": "Deneyap Mini v2",
        "variant": VARIANT_ESP32S2,
    },
    "denky32": {
        "name": "Denky32 (WROOM32)",
        "variant": VARIANT_ESP32,
    },
    "denky_d4": {
        "name": "Denky D4 (PICO-V3-02)",
        "variant": VARIANT_ESP32,
    },
    "dfrobot_beetle_esp32c3": {
        "name": "DFRobot Beetle ESP32-C3",
        "variant": VARIANT_ESP32C3,
    },
    "dfrobot_firebeetle2_esp32e": {
        "name": "DFRobot Firebeetle 2 ESP32-E",
        "variant": VARIANT_ESP32,
    },
    "dfrobot_firebeetle2_esp32s3": {
        "name": "DFRobot Firebeetle 2 ESP32-S3",
        "variant": VARIANT_ESP32S3,
    },
    "dfrobot_romeo_esp32s3": {
        "name": "DFRobot Romeo ESP32-S3",
        "variant": VARIANT_ESP32S3,
    },
    "dpu_esp32": {
        "name": "TAMC DPU ESP32",
        "variant": VARIANT_ESP32,
    },
    "edgebox-esp-100": {
        "name": "Seeed Studio Edgebox-ESP-100",
        "variant": VARIANT_ESP32S3,
    },
    "esp-wrover-kit": {
        "name": "Espressif ESP-WROVER-KIT",
        "variant": VARIANT_ESP32,
    },
    "esp32-c2-devkitm-1": {
        "name": "Espressif ESP32-C2-DevKitM-1",
        "variant": VARIANT_ESP32C2,
    },
    "esp32-c3-devkitc-02": {
        "name": "Espressif ESP32-C3-DevKitC-02",
        "variant": VARIANT_ESP32C3,
    },
    "esp32-c3-devkitm-1": {
        "name": "Espressif ESP32-C3-DevKitM-1",
        "variant": VARIANT_ESP32C3,
    },
    "esp32-c3-m1i-kit": {
        "name": "Ai-Thinker ESP-C3-M1-I-Kit",
        "variant": VARIANT_ESP32C3,
    },
    "esp32-c6-devkitc-1": {
        "name": "Espressif ESP32-C6-DevKitC-1",
        "variant": VARIANT_ESP32C6,
    },
    "esp32-c6-devkitm-1": {
        "name": "Espressif ESP32-C6-DevKitM-1",
        "variant": VARIANT_ESP32C6,
    },
    "esp32-devkitlipo": {
        "name": "OLIMEX ESP32-DevKit-LiPo",
        "variant": VARIANT_ESP32,
    },
    "esp32-evb": {
        "name": "OLIMEX ESP32-EVB",
        "variant": VARIANT_ESP32,
    },
    "esp32-gateway": {
        "name": "OLIMEX ESP32-GATEWAY",
        "variant": VARIANT_ESP32,
    },
    "esp32-h2-devkitm-1": {
        "name": "Espressif ESP32-H2-DevKit",
        "variant": VARIANT_ESP32H2,
    },
    "esp32-p4": {
        "name": "Espressif ESP32-P4 generic",
        "variant": VARIANT_ESP32P4,
    },
    "esp32-p4-evboard": {
        "name": "Espressif ESP32-P4 Function EV Board",
        "variant": VARIANT_ESP32P4,
    },
    "esp32-pico-devkitm-2": {
        "name": "Espressif ESP32-PICO-DevKitM-2",
        "variant": VARIANT_ESP32,
    },
    "esp32-poe": {
        "name": "OLIMEX ESP32-PoE",
        "variant": VARIANT_ESP32,
    },
    "esp32-poe-iso": {
        "name": "OLIMEX ESP32-PoE-ISO",
        "variant": VARIANT_ESP32,
    },
    "esp32-pro": {
        "name": "OLIMEX ESP32-PRO",
        "variant": VARIANT_ESP32,
    },
    "esp32-s2-franzininho": {
        "name": "Franzininho WiFi Board",
        "variant": VARIANT_ESP32S2,
    },
    "esp32-s2-kaluga-1": {
        "name": "Espressif ESP32-S2-Kaluga-1 Kit",
        "variant": VARIANT_ESP32S2,
    },
    "esp32-s2-saola-1": {
        "name": "Espressif ESP32-S2-Saola-1",
        "variant": VARIANT_ESP32S2,
    },
    "esp32-s3-devkitc-1": {
        "name": "Espressif ESP32-S3-DevKitC-1-N8 (8 MB QD, No PSRAM)",
        "variant": VARIANT_ESP32S3,
    },
    "esp32-s3-devkitm-1": {
        "name": "Espressif ESP32-S3-DevKitM-1",
        "variant": VARIANT_ESP32S3,
    },
    "esp32-solo1": {
        "name": "Espressif Generic ESP32-solo1 4M Flash",
        "variant": VARIANT_ESP32,
    },
    "esp320": {
        "name": "Electronic SweetPeas ESP320",
        "variant": VARIANT_ESP32,
    },
    "esp32cam": {
        "name": "AI Thinker ESP32-CAM",
        "variant": VARIANT_ESP32,
    },
    "esp32dev": {
        "name": "Espressif ESP32 Dev Module",
        "variant": VARIANT_ESP32,
    },
    "esp32doit-devkit-v1": {
        "name": "DOIT ESP32 DEVKIT V1",
        "variant": VARIANT_ESP32,
    },
    "esp32doit-espduino": {
        "name": "DOIT ESPduino32",
        "variant": VARIANT_ESP32,
    },
    "esp32s3_120_16_8-qio_opi": {
        "name": "ESP32-S3 16MB QIO, 8MB OPI PSRAM",
        "variant": VARIANT_ESP32S3,
    },
    "esp32s3_powerfeather": {
        "name": "ESP32-S3 PowerFeather",
        "variant": VARIANT_ESP32S3,
    },
    "esp32s3box": {
        "name": "Espressif ESP32-S3-Box",
        "variant": VARIANT_ESP32S3,
    },
    "esp32s3camlcd": {
        "name": "ESP32S3 CAM LCD",
        "variant": VARIANT_ESP32S3,
    },
    "esp32s3usbotg": {
        "name": "Espressif ESP32-S3-USB-OTG",
        "variant": VARIANT_ESP32S3,
    },
    "esp32thing": {
        "name": "SparkFun ESP32 Thing",
        "variant": VARIANT_ESP32,
    },
    "esp32thing_plus": {
        "name": "SparkFun ESP32 Thing Plus",
        "variant": VARIANT_ESP32,
    },
    "esp32vn-iot-uno": {
        "name": "ESP32vn IoT Uno",
        "variant": VARIANT_ESP32,
    },
    "espea32": {
        "name": "April Brother ESPea32",
        "variant": VARIANT_ESP32,
    },
    "espectro32": {
        "name": "ESPectro32",
        "variant": VARIANT_ESP32,
    },
    "espino32": {
        "name": "ESPino32",
        "variant": VARIANT_ESP32,
    },
    "etboard": {
        "name": "ETBoard",
        "variant": VARIANT_ESP32,
    },
    "featheresp32": {
        "name": "Adafruit ESP32 Feather",
        "variant": VARIANT_ESP32,
    },
    "featheresp32-s2": {
        "name": "Adafruit ESP32-S2 Feather Development Board",
        "variant": VARIANT_ESP32S2,
    },
    "firebeetle32": {
        "name": "FireBeetle-ESP32",
        "variant": VARIANT_ESP32,
    },
    "fm-devkit": {
        "name": "ESP32 FM DevKit",
        "variant": VARIANT_ESP32,
    },
    "franzininho_wifi_esp32s2": {
        "name": "Franzininho WiFi",
        "variant": VARIANT_ESP32S2,
    },
    "franzininho_wifi_msc_esp32s2": {
        "name": "Franzininho WiFi MSC",
        "variant": VARIANT_ESP32S2,
    },
    "freenove_esp32_s3_wroom": {
        "name": "Freenove ESP32-S3 WROOM N8R8 (8MB Flash / 8MB PSRAM)",
        "variant": VARIANT_ESP32S3,
    },
    "freenove_esp32_wrover": {
        "name": "Freenove ESP32-Wrover",
        "variant": VARIANT_ESP32,
    },
    "frogboard": {
        "name": "Frog Board ESP32",
        "variant": VARIANT_ESP32,
    },
    "healthypi4": {
        "name": "ProtoCentral HealthyPi 4",
        "variant": VARIANT_ESP32,
    },
    "heltec_wifi_kit_32": {
        "name": "Heltec WiFi Kit 32",
        "variant": VARIANT_ESP32,
    },
    "heltec_wifi_kit_32_V3": {
        "name": "Heltec WiFi Kit 32 (V3)",
        "variant": VARIANT_ESP32S3,
    },
    "heltec_wifi_kit_32_v2": {
        "name": "Heltec WiFi Kit 32 (V2)",
        "variant": VARIANT_ESP32,
    },
    "heltec_wifi_lora_32": {
        "name": "Heltec WiFi LoRa 32",
        "variant": VARIANT_ESP32,
    },
    "heltec_wifi_lora_32_V2": {
        "name": "Heltec WiFi LoRa 32 (V2)",
        "variant": VARIANT_ESP32,
    },
    "heltec_wifi_lora_32_V3": {
        "name": "Heltec WiFi LoRa 32 (V3)",
        "variant": VARIANT_ESP32S3,
    },
    "heltec_wireless_stick": {
        "name": "Heltec Wireless Stick",
        "variant": VARIANT_ESP32,
    },
    "heltec_wireless_stick_lite": {
        "name": "Heltec Wireless Stick Lite",
        "variant": VARIANT_ESP32,
    },
    "honeylemon": {
        "name": "HONEYLemon",
        "variant": VARIANT_ESP32,
    },
    "hornbill32dev": {
        "name": "Hornbill ESP32 Dev",
        "variant": VARIANT_ESP32,
    },
    "hornbill32minima": {
        "name": "Hornbill ESP32 Minima",
        "variant": VARIANT_ESP32,
    },
    "huidu_hd_wf2": {
        "name": "Huidu HD-WF2",
        "variant": VARIANT_ESP32S3,
    },
    "huidu_hd_wf4": {
        "name": "Huidu HD-WF4",
        "variant": VARIANT_ESP32S3,
    },
    "imbrios-logsens-v1p1": {
        "name": "Imbrios LogSens V1P1",
        "variant": VARIANT_ESP32,
    },
    "inex_openkb": {
        "name": "INEX OpenKB",
        "variant": VARIANT_ESP32,
    },
    "intorobot": {
        "name": "IntoRobot Fig",
        "variant": VARIANT_ESP32,
    },
    "iotaap_magnolia": {
        "name": "IoTaaP Magnolia",
        "variant": VARIANT_ESP32,
    },
    "iotbusio": {
        "name": "oddWires IoT-Bus Io",
        "variant": VARIANT_ESP32,
    },
    "iotbusproteus": {
        "name": "oddWires IoT-Bus Proteus",
        "variant": VARIANT_ESP32,
    },
    "ioxesp32": {
        "name": "ArtronShop IOXESP32",
        "variant": VARIANT_ESP32,
    },
    "ioxesp32ps": {
        "name": "ArtronShop IOXESP32PS",
        "variant": VARIANT_ESP32,
    },
    "jczn_2432s028r": {
        "name": "ESP32-2432S028R CYD",
        "variant": VARIANT_ESP32,
    },
    "kb32-ft": {
        "name": "MakerAsia KB32-FT",
        "variant": VARIANT_ESP32,
    },
    "kits-edu": {
        "name": "KITS ESP32 EDU",
        "variant": VARIANT_ESP32,
    },
    "labplus_mpython": {
        "name": "Labplus mPython",
        "variant": VARIANT_ESP32,
    },
    "lilka_v2": {
        "name": "Lilka v2",
        "variant": VARIANT_ESP32S3,
    },
    "lilygo-t-display": {
        "name": "LilyGo T-Display",
        "variant": VARIANT_ESP32,
    },
    "lilygo-t-display-s3": {
        "name": "LilyGo T-Display-S3",
        "variant": VARIANT_ESP32S3,
    },
    "lilygo-t3-s3": {
        "name": "LilyGo T3-S3",
        "variant": VARIANT_ESP32S3,
    },
    "lionbit": {
        "name": "Lion:Bit Dev Board",
        "variant": VARIANT_ESP32,
    },
    "lionbits3": {
        "name": "Lion:Bit S3 STEM Dev Board",
        "variant": VARIANT_ESP32S3,
    },
    "lolin32": {
        "name": "WEMOS LOLIN32",
        "variant": VARIANT_ESP32,
    },
    "lolin32_lite": {
        "name": "WEMOS LOLIN32 Lite",
        "variant": VARIANT_ESP32,
    },
    "lolin_c3_mini": {
        "name": "WEMOS LOLIN C3 Mini",
        "variant": VARIANT_ESP32C3,
    },
    "lolin_d32": {
        "name": "WEMOS LOLIN D32",
        "variant": VARIANT_ESP32,
    },
    "lolin_d32_pro": {
        "name": "WEMOS LOLIN D32 PRO",
        "variant": VARIANT_ESP32,
    },
    "lolin_s2_mini": {
        "name": "WEMOS LOLIN S2 Mini",
        "variant": VARIANT_ESP32S2,
    },
    "lolin_s2_pico": {
        "name": "WEMOS LOLIN S2 PICO",
        "variant": VARIANT_ESP32S2,
    },
    "lolin_s3": {
        "name": "WEMOS LOLIN S3",
        "variant": VARIANT_ESP32S3,
    },
    "lolin_s3_mini": {
        "name": "WEMOS LOLIN S3 Mini",
        "variant": VARIANT_ESP32S3,
    },
    "lolin_s3_mini_pro": {
        "name": "WEMOS LOLIN S3 Mini Pro",
        "variant": VARIANT_ESP32S3,
    },
    "lolin_s3_pro": {
        "name": "WEMOS LOLIN S3 PRO",
        "variant": VARIANT_ESP32S3,
    },
    "lopy": {
        "name": "Pycom LoPy",
        "variant": VARIANT_ESP32,
    },
    "lopy4": {
        "name": "Pycom LoPy4",
        "variant": VARIANT_ESP32,
    },
    "m5stack-atom": {
        "name": "M5Stack-ATOM",
        "variant": VARIANT_ESP32,
    },
    "m5stack-atoms3": {
        "name": "M5Stack AtomS3",
        "variant": VARIANT_ESP32S3,
    },
    "m5stack-core-esp32": {
        "name": "M5Stack Core ESP32",
        "variant": VARIANT_ESP32,
    },
    "m5stack-core-esp32-16M": {
        "name": "M5Stack Core ESP32 16M",
        "variant": VARIANT_ESP32,
    },
    "m5stack-core2": {
        "name": "M5Stack Core2",
        "variant": VARIANT_ESP32,
    },
    "m5stack-coreink": {
        "name": "M5Stack-Core Ink",
        "variant": VARIANT_ESP32,
    },
    "m5stack-cores3": {
        "name": "M5Stack CoreS3",
        "variant": VARIANT_ESP32S3,
    },
    "m5stack-fire": {
        "name": "M5Stack FIRE",
        "variant": VARIANT_ESP32,
    },
    "m5stack-grey": {
        "name": "M5Stack GREY ESP32",
        "variant": VARIANT_ESP32,
    },
    "m5stack-stamps3": {
        "name": "M5Stack StampS3",
        "variant": VARIANT_ESP32S3,
    },
    "m5stack-station": {
        "name": "M5Stack Station",
        "variant": VARIANT_ESP32,
    },
    "m5stack-timer-cam": {
        "name": "M5Stack Timer CAM",
        "variant": VARIANT_ESP32,
    },
    "m5stack_paper": {
        "name": "M5Stack Paper",
        "variant": VARIANT_ESP32,
    },
    "m5stamp-pico": {
        "name": "M5Stamp-Pico",
        "variant": VARIANT_ESP32,
    },
    "m5stick-c": {
        "name": "M5Stick-C",
        "variant": VARIANT_ESP32,
    },
    "magicbit": {
        "name": "MagicBit",
        "variant": VARIANT_ESP32,
    },
    "mgbot-iotik32a": {
        "name": "MGBOT IOTIK 32A",
        "variant": VARIANT_ESP32,
    },
    "mgbot-iotik32b": {
        "name": "MGBOT IOTIK 32B",
        "variant": VARIANT_ESP32,
    },
    "mhetesp32devkit": {
        "name": "MH ET LIVE ESP32DevKIT",
        "variant": VARIANT_ESP32,
    },
    "mhetesp32minikit": {
        "name": "MH ET LIVE ESP32MiniKit",
        "variant": VARIANT_ESP32,
    },
    "microduino-core-esp32": {
        "name": "Microduino Core ESP32",
        "variant": VARIANT_ESP32,
    },
    "micros2": {
        "name": "microS2",
        "variant": VARIANT_ESP32S2,
    },
    "minimain_esp32s2": {
        "name": "Deparment of Alchemy MiniMain ESP32-S2",
        "variant": VARIANT_ESP32S2,
    },
    "motorgo_mini_1": {
        "name": "MotorGo Mini 1 (ESP32-S3)",
        "variant": VARIANT_ESP32S3,
    },
    "namino_arancio": {
        "name": "Namino Arancio",
        "variant": VARIANT_ESP32S3,
    },
    "namino_rosso": {
        "name": "Namino Rosso",
        "variant": VARIANT_ESP32S3,
    },
    "nano32": {
        "name": "MakerAsia Nano32",
        "variant": VARIANT_ESP32,
    },
    "nebulas3": {
        "name": "Kinetic Dynamics Nebula S3",
        "variant": VARIANT_ESP32S3,
    },
    "nina_w10": {
        "name": "u-blox NINA-W10 series",
        "variant": VARIANT_ESP32,
    },
    "node32s": {
        "name": "Node32s",
        "variant": VARIANT_ESP32,
    },
    "nodemcu-32s": {
        "name": "NodeMCU-32S",
        "variant": VARIANT_ESP32,
    },
    "nodemcu-32s2": {
        "name": "Ai-Thinker NodeMCU-32S2 (ESP-12K)",
        "variant": VARIANT_ESP32S2,
    },
    "nscreen-32": {
        "name": "YeaCreate NSCREEN-32",
        "variant": VARIANT_ESP32,
    },
    "odroid_esp32": {
        "name": "ODROID-GO",
        "variant": VARIANT_ESP32,
    },
    "onehorse32dev": {
        "name": "Onehorse ESP32 Dev Module",
        "variant": VARIANT_ESP32,
    },
    "oroca_edubot": {
        "name": "OROCA EduBot",
        "variant": VARIANT_ESP32,
    },
    "pico32": {
        "name": "ESP32 Pico Kit",
        "variant": VARIANT_ESP32,
    },
    "piranha_esp32": {
        "name": "Fishino Piranha ESP-32",
        "variant": VARIANT_ESP32,
    },
    "pocket_32": {
        "name": "Dongsen Tech Pocket 32",
        "variant": VARIANT_ESP32,
    },
    "pycom_gpy": {
        "name": "Pycom GPy",
        "variant": VARIANT_ESP32,
    },
    "qchip": {
        "name": "Qchip",
        "variant": VARIANT_ESP32,
    },
    "quantum": {
        "name": "Noduino Quantum",
        "variant": VARIANT_ESP32,
    },
    "redpill_esp32s3": {
        "name": "Munich Labs RedPill ESP32-S3",
        "variant": VARIANT_ESP32S3,
    },
    "roboheart_hercules": {
        "name": "RoboHeart Hercules",
        "variant": VARIANT_ESP32,
    },
    "rymcu-esp32-s3-devkitc-1": {
        "name": "RYMCU ESP32-S3-DevKitC-1-N8R2 (8 MB QD, 2 MB PSRAM)",
        "variant": VARIANT_ESP32S3,
    },
    "s_odi_ultra": {
        "name": "S.ODI Ultra v1",
        "variant": VARIANT_ESP32,
    },
    "seeed_xiao_esp32c3": {
        "name": "Seeed Studio XIAO ESP32C3",
        "variant": VARIANT_ESP32C3,
    },
    "seeed_xiao_esp32c6": {
        "name": "Seeed Studio XIAO ESP32C6",
        "variant": VARIANT_ESP32C6,
    },
    "seeed_xiao_esp32s3": {
        "name": "Seeed Studio XIAO ESP32S3",
        "variant": VARIANT_ESP32S3,
    },
    "sensebox_mcu_esp32s2": {
        "name": "senseBox MCU-S2 ESP32-S2",
        "variant": VARIANT_ESP32S2,
    },
    "sensesiot_weizen": {
        "name": "LOGISENSES Senses Weizen",
        "variant": VARIANT_ESP32,
    },
    "sg-o_airMon": {
        "name": "SG-O AirMon",
        "variant": VARIANT_ESP32,
    },
    "sparkfun_esp32_iot_redboard": {
        "name": "SparkFun ESP32 IoT RedBoard",
        "variant": VARIANT_ESP32,
    },
    "sparkfun_esp32c6_thing_plus": {
        "name": "Sparkfun ESP32-C6 Thing Plus",
        "variant": VARIANT_ESP32C6,
    },
    "sparkfun_esp32micromod": {
        "name": "SparkFun ESP32 MicroMod",
        "variant": VARIANT_ESP32,
    },
    "sparkfun_esp32s2_thing_plus": {
        "name": "SparkFun ESP32-S2 Thing Plus",
        "variant": VARIANT_ESP32S2,
    },
    "sparkfun_esp32s2_thing_plus_c": {
        "name": "SparkFun ESP32 Thing Plus C",
        "variant": VARIANT_ESP32,
    },
    "sparkfun_esp32s3_thing_plus": {
        "name": "SPARKFUN_ESP32S3_THING_PLUS",
        "variant": VARIANT_ESP32S3,
    },
    "sparkfun_lora_gateway_1-channel": {
        "name": "SparkFun LoRa Gateway 1-Channel",
        "variant": VARIANT_ESP32,
    },
    "sparkfun_qwiic_pocket_esp32c6": {
        "name": "SparkFun ESP32-C6 Qwiic Pocket",
        "variant": VARIANT_ESP32C6,
    },
    "tamc_termod_s3": {
        "name": "TAMC Termod S3",
        "variant": VARIANT_ESP32S3,
    },
    "tinypico": {
        "name": "Unexpected Maker TinyPICO",
        "variant": VARIANT_ESP32,
    },
    "trueverit-iot-driver": {
        "name": "Trueverit ESP32 Universal IoT Driver",
        "variant": VARIANT_ESP32,
    },
    "trueverit-iot-driver-mk2": {
        "name": "Trueverit ESP32 Universal IoT Driver MK II",
        "variant": VARIANT_ESP32,
    },
    "trueverit-iot-driver-mk3": {
        "name": "Trueverit ESP32 Universal IoT Driver MK III",
        "variant": VARIANT_ESP32,
    },
    "ttgo-lora32-v1": {
        "name": "TTGO LoRa32-OLED V1",
        "variant": VARIANT_ESP32,
    },
    "ttgo-lora32-v2": {
        "name": "TTGO LoRa32-OLED V2",
        "variant": VARIANT_ESP32,
    },
    "ttgo-lora32-v21": {
        "name": "TTGO LoRa32-OLED v2.1.6",
        "variant": VARIANT_ESP32,
    },
    "ttgo-t-beam": {
        "name": "TTGO T-Beam",
        "variant": VARIANT_ESP32,
    },
    "ttgo-t-oi-plus": {
        "name": "TTGO T-OI PLUS RISC-V ESP32-C3",
        "variant": VARIANT_ESP32C3,
    },
    "ttgo-t-watch": {
        "name": "TTGO T-Watch",
        "variant": VARIANT_ESP32,
    },
    "ttgo-t1": {
        "name": "TTGO T1",
        "variant": VARIANT_ESP32,
    },
    "ttgo-t7-v13-mini32": {
        "name": "TTGO T7 V1.3 Mini32",
        "variant": VARIANT_ESP32,
    },
    "ttgo-t7-v14-mini32": {
        "name": "TTGO T7 V1.4 Mini32",
        "variant": VARIANT_ESP32,
    },
    "turta_iot_node": {
        "name": "Turta IoT Node",
        "variant": VARIANT_ESP32,
    },
    "um_feathers2": {
        "name": "Unexpected Maker FeatherS2",
        "variant": VARIANT_ESP32S2,
    },
    "um_feathers2_neo": {
        "name": "Unexpected Maker FeatherS2 Neo",
        "variant": VARIANT_ESP32S2,
    },
    "um_feathers3": {
        "name": "Unexpected Maker FeatherS3",
        "variant": VARIANT_ESP32S3,
    },
    "um_nanos3": {
        "name": "Unexpected Maker NanoS3",
        "variant": VARIANT_ESP32S3,
    },
    "um_pros3": {
        "name": "Unexpected Maker PROS3",
        "variant": VARIANT_ESP32S3,
    },
    "um_rmp": {
        "name": "Unexpected Maker RMP",
        "variant": VARIANT_ESP32S2,
    },
    "um_tinys2": {
        "name": "Unexpected Maker TinyS2",
        "variant": VARIANT_ESP32S2,
    },
    "um_tinys3": {
        "name": "Unexpected Maker TinyS3",
        "variant": VARIANT_ESP32S3,
    },
    "unphone7": {
        "name": "unPhone 7",
        "variant": VARIANT_ESP32,
    },
    "unphone8": {
        "name": "unPhone 8",
        "variant": VARIANT_ESP32S3,
    },
    "unphone9": {
        "name": "unPhone 9",
        "variant": VARIANT_ESP32S3,
    },
    "upesy_wroom": {
        "name": "uPesy ESP32 Wroom DevKit",
        "variant": VARIANT_ESP32,
    },
    "upesy_wrover": {
        "name": "uPesy ESP32 Wrover DevKit",
        "variant": VARIANT_ESP32,
    },
    "valtrack_v4_mfw_esp32_c3": {
        "name": "Valetron Systems VALTRACK-V4MVF",
        "variant": VARIANT_ESP32C3,
    },
    "valtrack_v4_vts_esp32_c3": {
        "name": "Valetron Systems VALTRACK-V4VTS",
        "variant": VARIANT_ESP32C3,
    },
    "vintlabs-devkit-v1": {
        "name": "VintLabs ESP32 Devkit",
        "variant": VARIANT_ESP32,
    },
    "watchy": {
        "name": "SQFMI Watchy v2.0",
        "variant": VARIANT_ESP32,
    },
    "waveshare_esp32_s3_zero": {
        "name": "Waveshare ESP32-S3-Zero",
        "variant": VARIANT_ESP32S3,
    },
    "waveshare_esp32s3_touch_lcd_128": {
        "name": "Waveshare ESP32-S3-Touch-LCD-1.28 (16 MB QD, 2MB PSRAM)",
        "variant": VARIANT_ESP32S3,
    },
    "weactstudio_esp32c3coreboard": {
        "name": "WeAct Studio ESP32C3CoreBoard",
        "variant": VARIANT_ESP32C3,
    },
    "wemos_d1_mini32": {
        "name": "WEMOS D1 MINI ESP32",
        "variant": VARIANT_ESP32,
    },
    "wemos_d1_uno32": {
        "name": "WEMOS D1 R32",
        "variant": VARIANT_ESP32,
    },
    "wemosbat": {
        "name": "WeMos WiFi and Bluetooth Battery",
        "variant": VARIANT_ESP32,
    },
    "wesp32": {
        "name": "Silicognition wESP32",
        "variant": VARIANT_ESP32,
    },
    "widora-air": {
        "name": "Widora AIR",
        "variant": VARIANT_ESP32,
    },
    "wifiduino32": {
        "name": "Blinker WiFiduino32",
        "variant": VARIANT_ESP32,
    },
    "wifiduino32c3": {
        "name": "Blinker WiFiduinoV2 (ESP32-C3)",
        "variant": VARIANT_ESP32C3,
    },
    "wifiduino32s3": {
        "name": "Blinker WiFiduino32S3",
        "variant": VARIANT_ESP32S3,
    },
    "wipy3": {
        "name": "Pycom WiPy3",
        "variant": VARIANT_ESP32,
    },
    "ws_esp32_s3_matrix": {
        "name": "Waveshare ESP32-S3-Matrix",
        "variant": VARIANT_ESP32S3,
    },
    "wt32-eth01": {
        "name": "Wireless-Tag WT32-ETH01 Ethernet Module",
        "variant": VARIANT_ESP32,
    },
    "wt32-sc01-plus": {
        "name": "wt32-sc01-plus",
        "variant": VARIANT_ESP32S3,
    },
    "xinabox_cw02": {
        "name": "XinaBox CW02",
        "variant": VARIANT_ESP32,
    },
    "yb_esp32s3_amp_v2": {
        "name": "YelloByte YB-ESP32-S3-AMP (Rev.2)",
        "variant": VARIANT_ESP32S3,
    },
    "yb_esp32s3_amp_v3": {
        "name": "YelloByte YB-ESP32-S3-AMP (Rev.3)",
        "variant": VARIANT_ESP32S3,
    },
    "yb_esp32s3_eth": {
        "name": "YelloByte YB-ESP32-S3-ETH",
        "variant": VARIANT_ESP32S3,
    },
}
