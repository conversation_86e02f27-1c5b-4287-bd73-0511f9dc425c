#pragma once

#include "esphome/core/component.h"
#include "esphome/core/log.h"

#ifdef USE_NRF52

namespace esphome {
namespace nrf52_ble_beacon {

static const char *const TAG = "nrf52_ble_beacon";

class NRF52BLEBeacon : public Component {
 public:
  void setup() override;
  void loop() override;
  void dump_config() override;
  
  void set_device_name(const std::string &name) { this->device_name_ = name; }
  void set_advertising_interval(uint32_t interval) { this->adv_interval_ = interval; }
  void set_tx_power(int8_t tx_power) { this->tx_power_ = tx_power; }
  
  void update_advertising_data();
  
 protected:
  std::string device_name_;
  uint32_t adv_interval_{1000};
  int8_t tx_power_{-12};
  
  bool advertising_started_{false};
  uint32_t last_update_{0};
  
  void start_advertising_();
  void stop_advertising_();
};

}  // namespace nrf52_ble_beacon
}  // namespace esphome

#endif  // USE_NRF52
