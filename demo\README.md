# NRF52833 Beacon Demo with BLE OTA

这是一个基于ESPHome的NRF52833 beacon设备配置，支持电池供电和BLE OTA更新。

## 功能特性

- ✅ **BLE OTA支持**: 使用MCUboot + mcumgr协议
- ✅ **电池监控**: 实时监测电池电压
- ✅ **低功耗设计**: 优化的电源管理
- ✅ **Key数据广播**: 在BLE广播中实时反馈key数据
- ✅ **状态指示**: LED状态指示
- ⚠️ **BLE Beacon**: 需要自定义组件实现

## 文件结构

```
demo/
├── nrf52833_demo.yaml              # 主配置文件
├── custom_components/              # 自定义组件
│   └── nrf52_beacon/
│       ├── __init__.py            # 组件配置
│       ├── nrf52_beacon.h         # 头文件
│       └── nrf52_beacon.cpp       # 实现文件
└── README.md                      # 说明文档
```

## 硬件要求

- NRF52833或兼容的开发板
- 电池供电系统
- 状态指示LED（可选）
- SWD调试器（用于初始烧录）

## 配置文件说明

项目提供了三个配置文件：

### 1. `nrf52833_simple.yaml` - 最简配置
- 纯蓝牙功能，无网络组件
- 电池监控和key数据传感器
- BLE OTA支持
- 适合初学者和基础应用

### 2. `nrf52833_demo.yaml` - 标准配置
- 包含更多传感器和功能
- 状态LED指示
- 更详细的日志和监控

### 3. `nrf52833_beacon_full.yaml` - 完整配置
- 包含自定义BLE beacon组件
- 实时iBeacon广播
- 完整的功能集

### 基本平台配置：
```yaml
nrf52:
  board: adafruit_feather_nrf52840  # 根据实际板子调整
  bootloader: mcuboot  # 必须使用mcuboot才支持BLE OTA
```

### 关键配置项

1. **Bootloader配置**
   - 使用`mcuboot`而不是`adafruit`
   - 这会生成`app_update.bin`文件用于BLE OTA

2. **电池监控**
   - 使用NRF52内置ADC监控VDD
   - 低电量警告机制

3. **传感器数据**
   - 模板传感器模拟key数据
   - 可替换为实际的传感器读取逻辑

## BLE OTA使用方法

### 1. 初始烧录

```bash
# 编译配置
esphome compile nrf52833_demo.yaml

# 使用SWD烧录器烧录.hex文件
# 或使用pyocd
pyocd flash -t nrf52833 .esphome/build/nrf52833-beacon/zephyr/zephyr.hex
```

### 2. BLE OTA更新

1. **准备更新文件**
   - 编译后会生成`app_update.bin`文件
   - 位置: `.esphome/build/nrf52833-beacon/zephyr/app_update.bin`

2. **使用mcumgr-web工具**
   - 访问: https://mcumgr-web.netlify.app/
   - 点击"Connect"连接到设备
   - 选择"Image Upload"
   - 上传`app_update.bin`文件
   - 等待更新完成并重启

3. **使用命令行工具**
   ```bash
   # 安装mcumgr
   go install github.com/apache/mynewt-mcumgr-cli/mcumgr@latest
   
   # 通过BLE上传固件
   mcumgr --conntype ble --connstring peer_name=nrf52833-beacon image upload app_update.bin
   mcumgr --conntype ble --connstring peer_name=nrf52833-beacon image test <hash>
   mcumgr --conntype ble --connstring peer_name=nrf52833-beacon reset
   ```

## BLE Beacon实现

### 使用自定义组件

要启用BLE beacon功能，需要在YAML中添加：

```yaml
external_components:
  - source:
      type: local
      path: custom_components
    components: [ nrf52_beacon ]

nrf52_beacon:
  uuid: "c29ce823-e67a-4e71-bff2-abaa32e77a98"
  major: 10
  minor: 1
  tx_power: -12
  advertising_interval: 1000ms
```

### 自定义组件功能

- **iBeacon协议**: 标准的Apple iBeacon格式
- **动态数据更新**: 可以实时更新minor字段中的key数据
- **可配置参数**: UUID、Major、Minor、发射功率、广播间隔
- **Zephyr BLE集成**: 使用Zephyr的蓝牙协议栈

## 电源优化建议

1. **降低日志级别**
   ```yaml
   logger:
     level: WARN  # 或 ERROR
     baud_rate: 0  # 禁用UART输出
   ```

2. **调整更新间隔**
   ```yaml
   sensor:
     - platform: adc
       update_interval: 300s  # 5分钟更新一次
   ```

3. **禁用不必要的组件**
   - 移除API组件（如果不需要）
   - 禁用状态LED
   - 减少BLE广播频率

## 故障排除

### 1. 编译错误
- 确保使用正确的board配置
- 检查Zephyr版本兼容性
- 验证自定义组件路径正确

### 2. BLE OTA连接失败
- 确认设备正在广播
- 检查设备名称是否正确
- 验证MCUboot是否正确配置
- 确保设备支持mcumgr服务

### 3. 电池监控不准确
- 调整ADC的multiply滤波器值
- 检查硬件电路的分压比
- 验证VDD引脚连接

### 4. BLE Beacon不工作
- 检查Zephyr BLE配置
- 验证自定义组件编译成功
- 查看日志输出确认初始化状态

## 开发指南

### 修改Key数据源

替换模板传感器为实际传感器：

```yaml
sensor:
  - platform: gpio  # 或其他实际传感器
    pin: 2
    name: "Key Sensor"
    id: key_data
    # ... 其他配置
```

### 自定义BLE广播数据

修改`nrf52_beacon.cpp`中的`update_key_data`函数来自定义数据格式。

### 添加更多传感器

可以添加温度、湿度等传感器，并将数据编码到BLE广播中。

## 注意事项

1. **首次使用必须通过SWD烧录**
2. **MCUboot和DFU功能互斥**
3. **自定义组件需要Zephyr BLE支持**
4. **电池供电时注意功耗优化**
5. **确保硬件支持所配置的功能**

## 相关链接

- [ESPHome NRF52文档](https://esphome.io/components/nrf52.html)
- [MCUmgr Web工具](https://mcumgr-web.netlify.app/)
- [Zephyr MCUboot文档](https://docs.zephyrproject.org/latest/services/device_mgmt/mcumgr.html)
- [Zephyr Bluetooth文档](https://docs.zephyrproject.org/latest/connectivity/bluetooth/index.html)
