cryptography==45.0.1
voluptuous==0.15.2
PyYAML==6.0.2
paho-mqtt==1.6.1
colorama==0.4.6
icmplib==3.0.4
tornado==6.5.2
tzlocal==5.3.1    # from time
tzdata>=2021.1  # from time
pyserial==3.5
platformio==6.1.18  # When updating platformio, also update /docker/Dockerfile
esptool==5.0.2
click==8.1.7
esphome-dashboard==20250828.0
aioesphomeapi==36.0.1
zeroconf==0.147.0
puremagic==1.30
ruamel.yaml==0.18.15 # dashboard_import
esphome-glyphsets==0.2.0
pillow==10.4.0
cairosvg==2.8.2
freetype-py==2.5.1
jinja2==3.1.6

# esp-idf requires this, but doesn't bundle it by default
# https://github.com/espressif/esp-idf/blob/220590d599e134d7a5e7f1e683cc4550349ffbf8/requirements.txt#L24
kconfiglib==13.7.1

# esp-idf >= 5.0 requires this
pyparsing >= 3.0

# For autocompletion
argcomplete>=2.0.0
