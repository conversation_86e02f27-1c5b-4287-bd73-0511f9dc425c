esphome:
  name: nrf52833-beacon
  friendly_name: "NRF52833 Simple Beacon"
  comment: "Simple NRF52833 configuration with BLE OTA support"

# NRF52833 platform configuration with MC<PERSON>boot for BLE OTA
nrf52:
  board: adafruit_feather_nrf52840  # Use compatible board, adjust for NRF52833
  bootloader: mcuboot  # Required for BLE OTA support

# BLE Configuration for NRF52 using Zephyr BLE stack
# Note: For NRF52 with Zephyr, we'll use a simplified approach
# focusing on advertising data since full BLE server may not be
# fully supported in current ESPHome NRF52 implementation

# Global variables to store BLE data
globals:
  - id: ble_key_data
    type: uint16_t
    initial_value: '0'
  - id: ble_state_data
    type: bool
    initial_value: 'false'



# Logging configuration - minimal for battery saving
logger:
  level: INFO
  baud_rate: 0  # Disable UART logging to save power

# Battery monitoring sensor
sensor:
  # Battery voltage monitoring using NRF52 ADC
  - platform: adc
    pin: VDD  # NRF52 internal VDD measurement
    name: "Battery Voltage"
    id: battery_voltage
    update_interval: 60s
    accuracy_decimals: 2
    filters:
      - multiply: 3.6  # Adjust based on your voltage divider setup

  # Key data sensor - replace with your actual sensor
  - platform: template
    name: "Key Data"
    id: key_data
    update_interval: 30s
    lambda: |-
      // Replace this with your actual key data reading logic
      static int counter = 0;
      counter = (counter + 1) % 100;
      return counter;
    on_value:
      then:
        # Update BLE characteristic when key data changes
        # Update global variables for BLE advertising
        - globals.set:
            id: ble_key_data
            value: !lambda 'return (uint16_t)x;'
        - logger.log:
            format: "BLE data updated - Key: %d"
            args: ['(int)x']

# Binary sensor for key state detection
binary_sensor:
  - platform: template
    name: "Key State"
    id: key_state
    lambda: |-
      // Replace with your actual key detection logic
      return id(key_data).state > 50;
    on_state:
      then:
        # Update global variable for BLE advertising
        - globals.set:
            id: ble_state_data
            value: !lambda 'return x;'
        - logger.log:
            format: "BLE state updated - State: %s"
            args: ['x ? "ON" : "OFF"']

# Text sensor for device information
text_sensor:
  - platform: template
    name: "Device Info"
    id: device_info
    update_interval: 60s
    lambda: |-
      char buffer[128];
      snprintf(buffer, sizeof(buffer),
               "NRF52833 Beacon v1.0|Bat:%.1fV|Key:%d|State:%s",
               id(battery_voltage).state,
               (int)id(key_data).state,
               id(key_state).state ? "ON" : "OFF");
      return std::string(buffer);

# Custom BLE beacon implementation using lambda
custom_component:
  - lambda: |-
      #ifdef USE_NRF52
      #include <zephyr/bluetooth/bluetooth.h>
      #include <zephyr/bluetooth/hci.h>

      class NRF52BLEBeacon : public Component {
       public:
        void setup() override {
          ESP_LOGI("ble_beacon", "Initializing BLE beacon...");

          // Initialize Bluetooth
          int err = bt_enable(nullptr);
          if (err) {
            ESP_LOGE("ble_beacon", "Bluetooth init failed (err %d)", err);
            return;
          }

          ESP_LOGI("ble_beacon", "BLE initialized successfully");
          this->start_advertising();
        }

        void loop() override {
          // Update advertising data every 30 seconds
          static uint32_t last_update = 0;
          if (millis() - last_update > 30000) {
            this->update_advertising_data();
            last_update = millis();
          }
        }

       private:
        void start_advertising() {
          // Basic advertising setup
          struct bt_le_adv_param adv_param = {
            .id = BT_ID_DEFAULT,
            .options = BT_LE_ADV_OPT_USE_IDENTITY,
            .interval_min = BT_GAP_ADV_FAST_INT_MIN_2,
            .interval_max = BT_GAP_ADV_FAST_INT_MAX_2,
          };

          // Advertising data
          struct bt_data ad[] = {
            BT_DATA_BYTES(BT_DATA_FLAGS, BT_LE_AD_GENERAL | BT_LE_AD_NO_BREDR),
            BT_DATA(BT_DATA_NAME_COMPLETE, "NRF52833-Beacon", 15),
          };

          int err = bt_le_adv_start(&adv_param, ad, ARRAY_SIZE(ad), nullptr, 0);
          if (err) {
            ESP_LOGE("ble_beacon", "Advertising failed to start (err %d)", err);
          } else {
            ESP_LOGI("ble_beacon", "BLE advertising started");
          }
        }

        void update_advertising_data() {
          // Stop current advertising
          bt_le_adv_stop();

          // Restart with updated data
          this->start_advertising();

          ESP_LOGD("ble_beacon", "Advertising data updated");
        }
      };

      return new NRF52BLEBeacon();
      #else
      return nullptr;
      #endif

# Interval for periodic status logging
interval:
  - interval: 60s
    then:
      - logger.log:
          format: "Status - Battery: %.1fV, Key: %d, State: %s"
          args:
            - 'id(battery_voltage).state'
            - '(int)id(key_data).state'
            - 'id(key_state).state ? "ON" : "OFF"'

# BLE Features:
# 1. Custom BLE Beacon Implementation
#    - Uses Zephyr Bluetooth stack directly via C++ code
#    - Advertises as "NRF52833-Beacon"
#    - Updates advertising data every 30 seconds
# 2. Global Variables for BLE Data
#    - ble_key_data: stores current key sensor value
#    - ble_state_data: stores current key state (on/off)
# 3. Automatic Data Updates
#    - Key data and state automatically update BLE globals
#    - Ready for integration with advertising data
#
# BLE OTA Instructions:
# 1. Compile: esphome compile nrf52833_simple.yaml
# 2. Initial flash via SWD: use the generated .hex file
# 3. BLE OTA updates:
#    - Use mcumgr-web: https://mcumgr-web.netlify.app/
#    - Upload the generated app_update.bin file
#    - Device will reboot and apply update via MCUboot
#
# BLE Usage:
# - Device advertises as "NRF52833-Beacon"
# - Use BLE scanner apps to detect the beacon
# - Advertising data can be extended to include sensor data
# - Custom C++ implementation allows full control over BLE behavior
#
# Note: This configuration includes:
# - Battery monitoring with ADC
# - Key data sensing with template sensor
# - Custom BLE beacon implementation using Zephyr
# - BLE OTA capability via MCUboot
# - Low power operation optimized for battery use
# - Global variables for easy BLE data management
