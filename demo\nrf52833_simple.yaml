esphome:
  name: nrf52833-beacon
  friendly_name: "NRF52833 Simple Beacon"
  comment: "Simple NRF52833 configuration with BLE OTA support and J-Link RTT logging"

  # Include RTT initialization header
  includes:
    - rtt_init.h

  # Enable J-Link RTT for debugging
  platformio_options:
    build_flags:
      - "-DCONFIG_USE_SEGGER_RTT=1"
      - "-DCONFIG_RTT_CONSOLE=1"
      - "-DCONFIG_UART_CONSOLE=0"
      - "-DCONFIG_LOG_BACKEND_RTT=1"
      - "-DCONFIG_LOG_BACKEND_UART=0"
      - "-DCONFIG_LOG_MODE_IMMEDIATE=1"
      - "-DSEGGER_RTT_CONFIG_DEFAULT_MODE=0"

  # Add startup message for RTT
  on_boot:
    priority: 800
    then:
      - lambda: |-
          #ifdef USE_NRF52
          init_rtt_logging();
          #endif
      - delay: 100ms
      - logger.log:
          level: INFO
          format: "=== NRF52833 Beacon Started with RTT Logging ==="
      - logger.log:
          level: INFO
          format: "Device: %s, Version: %s"
          args: ['"nrf52833-beacon"', '"1.0.0"']

# NRF52833 platform configuration with MCUboot for BLE OTA
nrf52:
  board: adafruit_feather_nrf52840  # Use compatible board, adjust for NRF52833
  bootloader: mcuboot  # Required for BLE OTA support

# BLE Configuration for NRF52 using Zephyr BLE stack
# Note: For NRF52 with Zephyr, we'll use a simplified approach
# focusing on advertising data since full BLE server may not be
# fully supported in current ESPHome NRF52 implementation

# Global variables to store BLE data
globals:
  - id: ble_key_data
    type: uint16_t
    initial_value: '0'
  - id: ble_state_data
    type: bool
    initial_value: 'false'



# Logging configuration with J-Link RTT support
logger:
  level: DEBUG  # Increase level for more detailed RTT output
  baud_rate: 0  # Disable UART logging to save power
  # Enable RTT logging for J-Link debugging
  logs:
    component: DEBUG
    sensor: DEBUG
    binary_sensor: DEBUG
    nrf52_ble_beacon: DEBUG

# Battery monitoring sensor
sensor:
  # Battery voltage monitoring using NRF52 ADC
  - platform: adc
    pin: VDD  # NRF52 internal VDD measurement
    name: "Battery Voltage"
    id: battery_voltage
    update_interval: 60s
    accuracy_decimals: 2
    filters:
      - multiply: 3.6  # Adjust based on your voltage divider setup

  # Key data sensor - replace with your actual sensor
  - platform: template
    name: "Key Data"
    id: key_data
    update_interval: 30s
    lambda: |-
      // Replace this with your actual key data reading logic
      static int counter = 0;
      counter = (counter + 1) % 100;
      return counter;
    on_value:
      then:
        # Update BLE characteristic when key data changes
        # Update global variables for BLE advertising
        - globals.set:
            id: ble_key_data
            value: !lambda 'return (uint16_t)x;'
        - logger.log:
            format: "BLE data updated - Key: %d"
            args: ['(int)x']

# Binary sensor for key state detection
binary_sensor:
  - platform: template
    name: "Key State"
    id: key_state
    lambda: |-
      // Replace with your actual key detection logic
      return id(key_data).state > 50;
    on_state:
      then:
        # Update global variable for BLE advertising
        - globals.set:
            id: ble_state_data
            value: !lambda 'return x;'
        - logger.log:
            format: "BLE state updated - State: %s"
            args: ['x ? "ON" : "OFF"']

# Text sensor for device information
text_sensor:
  - platform: template
    name: "Device Info"
    id: device_info
    update_interval: 60s
    lambda: |-
      char buffer[128];
      snprintf(buffer, sizeof(buffer),
               "NRF52833 Beacon v1.0|Bat:%.1fV|Key:%d|State:%s",
               id(battery_voltage).state,
               (int)id(key_data).state,
               id(key_state).state ? "ON" : "OFF");
      return std::string(buffer);

# BLE functionality note:
# For NRF52 BLE beacon functionality, you have several options:
# 1. Use the full beacon configuration (nrf52833_beacon_full.yaml)
# 2. Implement BLE at the Zephyr level using prj.conf
# 3. Use Nordic's SDK examples as a base
#
# This simplified version focuses on sensor data collection
# and BLE OTA capability. For full BLE beacon implementation,
# refer to the custom components in the full configuration.

# Interval for periodic status logging
interval:
  - interval: 60s
    then:
      - logger.log:
          format: "Status - Battery: %.1fV, Key: %d, State: %s"
          args:
            - 'id(battery_voltage).state'
            - '(int)id(key_data).state'
            - 'id(key_state).state ? "ON" : "OFF"'

# Configuration Features:
# 1. J-Link RTT Debugging
#    - Real-time log output via J-Link RTT
#    - No UART resources required
#    - DEBUG level logging enabled
#    - See RTT_GUIDE.md for usage instructions
# 2. Battery Monitoring
#    - Uses NRF52 internal VDD measurement
#    - Configurable voltage scaling
#    - Low battery detection with RTT logging
# 3. Key Data Sensing
#    - Template sensor for demonstration
#    - Replace with actual sensor implementation
#    - Binary state detection based on threshold
#    - Real-time RTT log output for sensor changes
# 4. Global Variables for Data Storage
#    - ble_key_data: stores current key sensor value
#    - ble_state_data: stores current key state (on/off)
#    - Ready for BLE integration
# 5. Device Information
#    - Text sensor with combined status information
#    - Periodic status logging via RTT
#
# BLE OTA Instructions:
# 1. Compile: esphome compile nrf52833_simple.yaml
# 2. Initial flash via SWD: use the generated .hex file
# 3. BLE OTA updates:
#    - Use mcumgr-web: https://mcumgr-web.netlify.app/
#    - Upload the generated app_update.bin file
#    - Device will reboot and apply update via MCUboot
#
# BLE Implementation Options:
# - For full BLE beacon: use nrf52833_beacon_full.yaml
# - For custom BLE: implement at Zephyr level
# - This config provides sensor foundation for BLE integration
#
# Note: This simplified configuration includes:
# - Battery monitoring optimized for low power
# - Sensor data collection and processing
# - BLE OTA capability via MCUboot
# - Foundation for BLE beacon implementation
# - Global variables for easy data access
