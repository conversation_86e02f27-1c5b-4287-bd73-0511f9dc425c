esphome:
  name: nrf52833-beacon
  friendly_name: "NRF52833 Simple Beacon"
  comment: "Simple NRF52833 configuration with BLE OTA support"

# NRF52833 platform configuration with MC<PERSON>boot for BLE OTA
nrf52:
  board: adafruit_feather_nrf52840  # Use compatible board, adjust for NRF52833
  bootloader: mcuboot  # Required for BLE OTA support

# Logging configuration - minimal for battery saving
logger:
  level: INFO
  baud_rate: 0  # Disable UART logging to save power

# Battery monitoring sensor
sensor:
  # Battery voltage monitoring using NRF52 ADC
  - platform: adc
    pin: VDD  # NRF52 internal VDD measurement
    name: "Battery Voltage"
    id: battery_voltage
    update_interval: 60s
    accuracy_decimals: 2
    filters:
      - multiply: 3.6  # Adjust based on your voltage divider setup

  # Key data sensor - replace with your actual sensor
  - platform: template
    name: "Key Data"
    id: key_data
    update_interval: 30s
    lambda: |-
      // Replace this with your actual key data reading logic
      static int counter = 0;
      counter = (counter + 1) % 100;
      return counter;

# Binary sensor for key state detection
binary_sensor:
  - platform: template
    name: "Key State"
    id: key_state
    lambda: |-
      // Replace with your actual key detection logic
      return id(key_data).state > 50;

# Interval for periodic status logging
interval:
  - interval: 60s
    then:
      - logger.log: 
          format: "Status - Battery: %.1fV, Key: %d, State: %s"
          args: 
            - 'id(battery_voltage).state'
            - '(int)id(key_data).state'
            - 'id(key_state).state ? "ON" : "OFF"'

# BLE OTA Instructions:
# 1. Compile: esphome compile nrf52833_simple.yaml
# 2. Initial flash via SWD: use the generated .hex file
# 3. BLE OTA updates:
#    - Use mcumgr-web: https://mcumgr-web.netlify.app/
#    - Upload the generated app_update.bin file
#    - Device will reboot and apply update via MCUboot
#
# Note: This is a minimal configuration focusing on:
# - Battery monitoring
# - Key data sensing
# - BLE OTA capability
# - Low power operation
