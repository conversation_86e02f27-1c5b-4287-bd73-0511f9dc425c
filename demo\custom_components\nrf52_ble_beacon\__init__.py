import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.const import CONF_ID
from esphome.core import CORE

DEPENDENCIES = ["nrf52"]
CODEOWNERS = ["@esphome"]

nrf52_ble_beacon_ns = cg.esphome_ns.namespace("nrf52_ble_beacon")
NRF52BLEBeacon = nrf52_ble_beacon_ns.class_("NRF52BLEBeacon", cg.Component)

CONF_DEVICE_NAME = "device_name"
CONF_ADVERTISING_INTERVAL = "advertising_interval"
CONF_TX_POWER = "tx_power"

CONFIG_SCHEMA = cv.Schema(
    {
        cv.GenerateID(): cv.declare_id(NRF52BLEBeacon),
        cv.Optional(CONF_DEVICE_NAME, default="NRF52-Beacon"): cv.string,
        cv.Optional(CONF_ADVERTISING_INTERVAL, default="1000ms"): cv.positive_time_period_milliseconds,
        cv.Optional(CONF_TX_POWER, default=-12): cv.int_range(min=-40, max=8),
    }
).extend(cv.COMPONENT_SCHEMA)


async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)

    cg.add(var.set_device_name(config[CONF_DEVICE_NAME]))
    cg.add(var.set_advertising_interval(config[CONF_ADVERTISING_INTERVAL]))
    cg.add(var.set_tx_power(config[CONF_TX_POWER]))

    # Add necessary build flags for NRF52 BLE
    if CORE.using_esp_idf:
        # This shouldn't happen for NRF52, but just in case
        pass
    else:
        # Add Zephyr BLE build flags
        cg.add_build_flag("-DCONFIG_BT=y")
        cg.add_build_flag("-DCONFIG_BT_PERIPHERAL=y")
        cg.add_build_flag("-DCONFIG_BT_DEVICE_NAME_DYNAMIC=y")
        cg.add_build_flag("-DCONFIG_BT_DEVICE_NAME_MAX=32")
