---
name: CI for docker images

# Only run when docker paths change

on:
  push:
    branches: [dev, beta, release]
    paths:
      - "docker/**"
      - ".github/workflows/ci-docker.yml"
      - "requirements*.txt"
      - "platformio.ini"
      - "script/platformio_install_deps.py"

  pull_request:
    paths:
      - "docker/**"
      - ".github/workflows/ci-docker.yml"
      - "requirements*.txt"
      - "platformio.ini"
      - "script/platformio_install_deps.py"

permissions:
  contents: read
  packages: read

concurrency:
  # yamllint disable-line rule:line-length
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  check-docker:
    name: Build docker containers
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: ["ubuntu-24.04", "ubuntu-24.04-arm"]
        build_type:
          - "ha-addon"
          - "docker"
          # - "lint"
    steps:
      - uses: actions/checkout@v5.0.0
      - name: Set up Python
        uses: actions/setup-python@v5.6.0
        with:
          python-version: "3.11"
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.11.1

      - name: Set TAG
        run: |
          echo "TAG=check" >> $GITHUB_ENV

      - name: Run build
        run: |
          docker/build.py \
            --tag "${TAG}" \
            --arch "${{ matrix.os == 'ubuntu-24.04-arm' && 'aarch64' || 'amd64' }}" \
            --build-type "${{ matrix.build_type }}" \
            build
