#pragma once

#include "esphome/core/component.h"
#include "esphome/core/log.h"

#ifdef USE_NRF52

#include <zephyr/bluetooth/bluetooth.h>
#include <zephyr/bluetooth/hci.h>
#include <zephyr/bluetooth/conn.h>
#include <zephyr/bluetooth/uuid.h>
#include <zephyr/bluetooth/gatt.h>

namespace esphome {
namespace nrf52_beacon {

static const char *const TAG = "nrf52_beacon";

class NRF52Beacon : public Component {
 public:
  void setup() override;
  void loop() override;
  void dump_config() override;
  
  void set_uuid(const std::string &uuid) { this->uuid_ = uuid; }
  void set_major(uint16_t major) { this->major_ = major; }
  void set_minor(uint16_t minor) { this->minor_ = minor; }
  void set_tx_power(int8_t tx_power) { this->tx_power_ = tx_power; }
  void set_advertising_interval(uint32_t interval) { this->adv_interval_ = interval; }
  
  void update_key_data(uint16_t key_data);
  
 protected:
  std::string uuid_;
  uint16_t major_{0};
  uint16_t minor_{0};
  int8_t tx_power_{-12};
  uint32_t adv_interval_{1000};
  uint16_t current_key_data_{0};
  
  bool advertising_started_{false};
  
  void start_advertising_();
  void update_advertising_data_();
  
  // iBeacon advertising data structure
  struct ibeacon_data {
    uint8_t flags[3];
    uint8_t length;
    uint8_t type;
    uint8_t company_id[2];
    uint8_t beacon_type[2];
    uint8_t uuid[16];
    uint8_t major[2];
    uint8_t minor[2];
    uint8_t tx_power;
  } __attribute__((packed));
  
  ibeacon_data adv_data_;
};

}  // namespace nrf52_beacon
}  // namespace esphome

#endif  // USE_NRF52
