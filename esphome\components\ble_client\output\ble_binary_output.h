#pragma once

#include "esphome/core/component.h"
#include "esphome/components/ble_client/ble_client.h"
#include "esphome/components/esp32_ble_tracker/esp32_ble_tracker.h"
#include "esphome/components/output/binary_output.h"

#ifdef USE_ESP32
#include <esp_gattc_api.h>
namespace esphome {
namespace ble_client {

namespace espbt = esphome::esp32_ble_tracker;

class BLEBinaryOutput : public output::BinaryOutput, public BLEClientNode, public Component {
 public:
  void dump_config() override;
  void loop() override {}
  void set_service_uuid16(uint16_t uuid) { this->service_uuid_ = espbt::ESPBTUUID::from_uint16(uuid); }
  void set_service_uuid32(uint32_t uuid) { this->service_uuid_ = espbt::ESPBTUUID::from_uint32(uuid); }
  void set_service_uuid128(uint8_t *uuid) { this->service_uuid_ = espbt::ESPBTUUID::from_raw(uuid); }
  void set_char_uuid16(uint16_t uuid) { this->char_uuid_ = espbt::ESPBTUUID::from_uint16(uuid); }
  void set_char_uuid32(uint32_t uuid) { this->char_uuid_ = espbt::ESPBTUUID::from_uint32(uuid); }
  void set_char_uuid128(uint8_t *uuid) { this->char_uuid_ = espbt::ESPBTUUID::from_raw(uuid); }
  void gattc_event_handler(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if,
                           esp_ble_gattc_cb_param_t *param) override;
  void set_require_response(bool response) { this->require_response_ = response; }

 protected:
  void write_state(bool state) override;
  bool require_response_;
  espbt::ESPBTUUID service_uuid_;
  espbt::ESPBTUUID char_uuid_;
  uint16_t char_handle_{};
  esp_gatt_char_prop_t char_props_{};
  esp_gatt_write_type_t write_type_{};
};

}  // namespace ble_client
}  // namespace esphome

#endif
