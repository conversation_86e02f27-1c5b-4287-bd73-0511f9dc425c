---
name: Synchronise Device Classes from Home Assistant

on:
  workflow_dispatch:
  schedule:
    - cron: "45 6 * * *"

jobs:
  sync:
    name: Sync Device Classes
    runs-on: ubuntu-latest
    if: github.repository == 'esphome/esphome'
    steps:
      - name: Checkout
        uses: actions/checkout@v5.0.0

      - name: Checkout Home Assistant
        uses: actions/checkout@v5.0.0
        with:
          repository: home-assistant/core
          path: lib/home-assistant

      - name: Setup Python
        uses: actions/setup-python@v5.6.0
        with:
          python-version: 3.13

      - name: Install Home Assistant
        run: |
          python -m pip install --upgrade pip
          pip install -e lib/home-assistant

      - name: Sync
        run: |
          python ./script/sync-device_class.py

      - name: Commit changes
        uses: peter-evans/create-pull-request@v7.0.8
        with:
          commit-message: "Synchronise Device Classes from Home Assistant"
          committer: esphomebot <<EMAIL>>
          author: esphomebot <<EMAIL>>
          branch: sync/device-classes
          delete-branch: true
          title: "Synchronise Device Classes from Home Assistant"
          body-path: .github/PULL_REQUEST_TEMPLATE.md
          token: ${{ secrets.DEVICE_CLASS_SYNC_TOKEN }}
