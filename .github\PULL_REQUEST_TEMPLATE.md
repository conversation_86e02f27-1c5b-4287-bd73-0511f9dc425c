# What does this implement/fix?

<!-- Quick description and explanation of changes -->

## Types of changes

- [ ] Bugfix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Code quality improvements to existing code or addition of tests
- [ ] Other

**Related issue or feature (if applicable):**

- fixes <link to issue>

**Pull request in [esphome-docs](https://github.com/esphome/esphome-docs) with documentation (if applicable):**

- esphome/esphome-docs#<esphome-docs PR number goes here>

## Test Environment

- [ ] ESP32
- [ ] ESP32 IDF
- [ ] ESP8266
- [ ] RP2040
- [ ] BK72xx
- [ ] RTL87xx
- [ ] nRF52840

## Example entry for `config.yaml`:

```yaml
# Example config.yaml

```

## Checklist:
  - [ ] The code change is tested and works locally.
  - [ ] Tests have been added to verify that the new code works (under `tests/` folder).

If user exposed functionality or configuration variables are added/changed:
  - [ ] Documentation added/updated in [esphome-docs](https://github.com/esphome/esphome-docs).
