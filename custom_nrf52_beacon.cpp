#include "custom_nrf52_beacon.h"

#ifdef USE_NRF52

namespace esphome {
namespace nrf52_beacon {

void NRF52Beacon::setup() {
  ESP_LOGCONFIG(TAG, "Setting up NRF52 Beacon...");
  
  // Initialize Bluetooth
  int err = bt_enable(nullptr);
  if (err) {
    ESP_LOGE(TAG, "Bluetooth init failed (err %d)", err);
    this->mark_failed();
    return;
  }
  
  ESP_LOGCONFIG(TAG, "Bluetooth initialized");
  
  // Prepare iBeacon advertising data
  this->adv_data_.flags[0] = 0x02;  // Length
  this->adv_data_.flags[1] = 0x01;  // Flags type
  this->adv_data_.flags[2] = 0x06;  // LE General Discoverable + BR/EDR not supported
  
  this->adv_data_.length = 0x1A;    // Length of manufacturer data
  this->adv_data_.type = 0xFF;      // Manufacturer specific data
  this->adv_data_.company_id[0] = 0x4C;  // Apple Company ID (little endian)
  this->adv_data_.company_id[1] = 0x00;
  this->adv_data_.beacon_type[0] = 0x02;  // iBeacon type
  this->adv_data_.beacon_type[1] = 0x15;  // iBeacon data length
  
  // Parse UUID string to bytes (simplified - you may want to add proper UUID parsing)
  // For now, using a default UUID
  uint8_t default_uuid[16] = {0xc2, 0x9c, 0xe8, 0x23, 0xe6, 0x7a, 0x4e, 0x71, 
                              0xbf, 0xf2, 0xab, 0xaa, 0x32, 0xe7, 0x7a, 0x98};
  memcpy(this->adv_data_.uuid, default_uuid, 16);
  
  // Set major and minor (big endian)
  this->adv_data_.major[0] = (this->major_ >> 8) & 0xFF;
  this->adv_data_.major[1] = this->major_ & 0xFF;
  this->adv_data_.minor[0] = (this->minor_ >> 8) & 0xFF;
  this->adv_data_.minor[1] = this->minor_ & 0xFF;
  
  this->adv_data_.tx_power = this->tx_power_;
  
  this->start_advertising_();
}

void NRF52Beacon::loop() {
  // Update advertising data if needed
  static uint32_t last_update = 0;
  uint32_t now = millis();
  
  if (now - last_update > this->adv_interval_) {
    this->update_advertising_data_();
    last_update = now;
  }
}

void NRF52Beacon::dump_config() {
  ESP_LOGCONFIG(TAG, "NRF52 Beacon:");
  ESP_LOGCONFIG(TAG, "  UUID: %s", this->uuid_.c_str());
  ESP_LOGCONFIG(TAG, "  Major: %d", this->major_);
  ESP_LOGCONFIG(TAG, "  Minor: %d", this->minor_);
  ESP_LOGCONFIG(TAG, "  TX Power: %d dBm", this->tx_power_);
  ESP_LOGCONFIG(TAG, "  Advertising Interval: %d ms", this->adv_interval_);
}

void NRF52Beacon::update_key_data(uint16_t key_data) {
  if (this->current_key_data_ != key_data) {
    this->current_key_data_ = key_data;
    // Update minor field with key data
    this->adv_data_.minor[0] = (key_data >> 8) & 0xFF;
    this->adv_data_.minor[1] = key_data & 0xFF;
    ESP_LOGD(TAG, "Updated key data to: %d", key_data);
  }
}

void NRF52Beacon::start_advertising_() {
  struct bt_le_adv_param adv_param = {
    .id = BT_ID_DEFAULT,
    .sid = 0,
    .secondary_max_skip = 0,
    .options = BT_LE_ADV_OPT_USE_IDENTITY,
    .interval_min = BT_GAP_ADV_FAST_INT_MIN_2,
    .interval_max = BT_GAP_ADV_FAST_INT_MAX_2,
    .peer = nullptr,
  };
  
  struct bt_data ad[] = {
    BT_DATA_BYTES(BT_DATA_FLAGS, BT_LE_AD_GENERAL | BT_LE_AD_NO_BREDR),
    BT_DATA(BT_DATA_MANUFACTURER_DATA, &this->adv_data_.company_id, 
            sizeof(this->adv_data_) - offsetof(ibeacon_data, company_id)),
  };
  
  int err = bt_le_adv_start(&adv_param, ad, ARRAY_SIZE(ad), nullptr, 0);
  if (err) {
    ESP_LOGE(TAG, "Advertising failed to start (err %d)", err);
    return;
  }
  
  this->advertising_started_ = true;
  ESP_LOGD(TAG, "Advertising started");
}

void NRF52Beacon::update_advertising_data_() {
  if (!this->advertising_started_) {
    return;
  }
  
  // Stop current advertising
  int err = bt_le_adv_stop();
  if (err) {
    ESP_LOGW(TAG, "Failed to stop advertising (err %d)", err);
  }
  
  // Restart with updated data
  this->start_advertising_();
}

}  // namespace nrf52_beacon
}  // namespace esphome

#endif  // USE_NRF52
