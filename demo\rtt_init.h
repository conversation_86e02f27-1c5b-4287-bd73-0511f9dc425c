#pragma once

#ifdef USE_NRF52

// Simple RTT initialization placeholder
// RTT should be automatically initialized by Zephyr logging system
static inline void init_rtt_logging() {
    // RTT initialization is handled by Zephyr's logging backend
    // This function serves as a placeholder for future RTT-specific setup

    // Add a small delay to ensure logging system is ready
    k_msleep(10);
}

#endif // USE_NRF52
