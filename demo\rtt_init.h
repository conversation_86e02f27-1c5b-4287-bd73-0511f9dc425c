#pragma once

#ifdef USE_NRF52

#include <zephyr/kernel.h>
#include <zephyr/logging/log.h>

// Force RTT initialization
static inline void init_rtt_logging() {
    // Ensure RTT is initialized
    #ifdef CONFIG_USE_SEGGER_RTT
    extern void SEGGER_RTT_Init(void);
    SEGGER_RTT_Init();
    
    // Send a test message directly to RTT
    extern int SEGGER_RTT_printf(unsigned BufferIndex, const char * sFormat, ...);
    SEGGER_RTT_printf(0, "RTT initialized successfully!\n");
    #endif
}

#endif // USE_NRF52
