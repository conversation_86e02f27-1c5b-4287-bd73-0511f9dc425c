#!/bin/bash

# RTT测试脚本
# 用于验证J-Link RTT连接和输出

echo "=== NRF52833 RTT测试脚本 ==="
echo

# 检查J-Link是否可用
if ! command -v JLinkExe &> /dev/null; then
    echo "错误: 未找到JLinkExe命令"
    echo "请安装J-Link Software Pack"
    exit 1
fi

echo "1. 测试J-Link连接..."

# 创建临时J-Link脚本
cat > /tmp/jlink_test.jlink << EOF
connect
NRF52833_XXAA
SWD
4000
halt
go
exit
EOF

# 执行J-Link连接测试
if JLinkExe -CommandFile /tmp/jlink_test.jlink > /tmp/jlink_output.txt 2>&1; then
    if grep -q "Cortex-M4" /tmp/jlink_output.txt; then
        echo "✓ J-Link连接成功，检测到Cortex-M4"
    else
        echo "✗ J-Link连接失败"
        echo "输出："
        cat /tmp/jlink_output.txt
        exit 1
    fi
else
    echo "✗ 无法执行J-Link命令"
    exit 1
fi

echo
echo "2. 启动RTT监控..."
echo "请确保设备正在运行，然后按Enter继续..."
read

# 检查RTT Client是否可用
if ! command -v JLinkRTTClient &> /dev/null; then
    echo "警告: 未找到JLinkRTTClient"
    echo "尝试使用RTT Viewer GUI工具"
    
    if command -v JLinkRTTViewer &> /dev/null; then
        echo "启动RTT Viewer..."
        JLinkRTTViewer &
    else
        echo "请手动启动RTT Viewer并配置："
        echo "- Target Device: NRF52833_XXAA"
        echo "- Interface: SWD"
        echo "- Speed: 4000 kHz"
    fi
else
    echo "启动RTT Client..."
    echo "RTT输出将显示在下方："
    echo "按Ctrl+C退出"
    echo "----------------------------------------"
    
    # 在后台启动J-Link连接
    JLinkExe -Device NRF52833_XXAA -if SWD -Speed 4000 -AutoConnect 1 > /dev/null 2>&1 &
    JLINK_PID=$!
    
    # 等待连接建立
    sleep 2
    
    # 启动RTT Client
    JLinkRTTClient
    
    # 清理
    kill $JLINK_PID 2>/dev/null
fi

# 清理临时文件
rm -f /tmp/jlink_test.jlink /tmp/jlink_output.txt

echo
echo "RTT测试完成"
