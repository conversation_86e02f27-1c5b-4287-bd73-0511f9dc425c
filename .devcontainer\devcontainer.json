{
  "name": "ESPHome Dev",
  "context": "..",
  "dockerFile": "Dockerfile",
  "postCreateCommand": [
    "script/devcontainer-post-create"
  ],
  "features": {
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  "runArgs": [
    "--privileged",
    "-e",
    "GIT_EDITOR=code --wait"
    // uncomment and edit the path in order to pass though local USB serial to the conatiner
    // , "--device=/dev/ttyACM0"
  ],
  "appPort": 6052,
  // if you are using avahi in the host device, uncomment these to allow the
  // devcontainer to find devices via mdns
  //"mounts": [
  //  "type=bind,source=/dev/bus/usb,target=/dev/bus/usb",
  //  "type=bind,source=/var/run/dbus,target=/var/run/dbus",
  //  "type=bind,source=/var/run/avahi-daemon/socket,target=/var/run/avahi-daemon/socket"
  //],
  "customizations": {
    "vscode": {
      "extensions": [
        // python
        "ms-python.python",
        "ms-python.pylint",
        "ms-python.flake8",
        "charliermarsh.ruff",
        "visualstudioexptteam.vscodeintellicode",
        // yaml
        "redhat.vscode-yaml",
        // cpp
        "ms-vscode.cpptools",
        // editorconfig
        "editorconfig.editorconfig"
      ],
      "settings": {
        "python.languageServer": "Pylance",
        "python.pythonPath": "/usr/bin/python3",
        "pylint.args": [
          "--rcfile=${workspaceFolder}/pyproject.toml"
        ],
        "flake8.args": [
          "--config=${workspaceFolder}/.flake8"
        ],
        "ruff.configuration": "${workspaceFolder}/pyproject.toml",
        "[python]": {
          // VS will say "Value is not accepted" before building the devcontainer, but the warning
          // should go away after build is completed.
          "editor.defaultFormatter": "charliermarsh.ruff"
        },
        "editor.formatOnPaste": false,
        "editor.formatOnSave": true,
        "editor.formatOnType": true,
        "files.trimTrailingWhitespace": true,
        "terminal.integrated.defaultProfile.linux": "bash",
        "yaml.customTags": [
          "!secret scalar",
          "!lambda scalar",
          "!extend scalar",
          "!remove scalar",
          "!include_dir_named scalar",
          "!include_dir_list scalar",
          "!include_dir_merge_list scalar",
          "!include_dir_merge_named scalar"
        ],
        "files.exclude": {
          "**/.git": true,
          "**/.DS_Store": true,
          "**/*.pyc": {
            "when": "$(basename).py"
          },
          "**/__pycache__": true
        },
        "files.associations": {
          "**/.vscode/*.json": "jsonc"
        },
        "C_Cpp.clang_format_path": "/usr/bin/clang-format-13"
      }
    }
  }
}
