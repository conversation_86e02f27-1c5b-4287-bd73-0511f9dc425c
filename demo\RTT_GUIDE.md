# J-Link RTT 调试指南

## RTT (Real Time Transfer) 简介

RTT是SEGGER开发的一种高效的调试输出技术，通过J-Link调试器实现实时双向通信，无需占用UART资源。

## 硬件要求

- J-Link调试器（J-Link EDU、J-Link PLUS等）
- NRF52833开发板
- SWD连接线

## 软件要求

- J-Link Software Pack
- RTT Viewer 或 J-Link Commander
- ESPHome (已配置RTT支持)

## 配置说明

### 1. ESPHome配置

`nrf52833_simple.yaml` 已经包含RTT配置：

```yaml
nrf52:
  platformio_options:
    build_flags:
      - "-DCONFIG_USE_SEGGER_RTT=1"
      - "-DCONFIG_RTT_CONSOLE=1" 
      - "-DCONFIG_UART_CONSOLE=0"
      - "-DCONFIG_LOG_BACKEND_RTT=1"

logger:
  level: DEBUG
  baud_rate: 0  # 禁用UART，使用RTT
```

### 2. Zephyr配置

`prj.conf` 文件包含Zephyr级别的RTT配置：

```
CONFIG_USE_SEGGER_RTT=y
CONFIG_RTT_CONSOLE=y
CONFIG_LOG_BACKEND_RTT=y
CONFIG_LOG_BACKEND_UART=n
```

## 使用步骤

### 1. 编译和烧录

```bash
# 编译固件
esphome compile nrf52833_simple.yaml

# 烧录固件（通过J-Link）
nrfjprog --program .esphome/build/nrf52833-beacon/zephyr/zephyr.hex --sectorerase --verify --reset
```

### 2. 启动RTT Viewer

**方法一：使用RTT Viewer GUI**

1. 启动 "J-Link RTT Viewer"
2. 配置连接参数：
   - Target Device: NRF52833_XXAA
   - Target Interface: SWD
   - Speed: 4000 kHz
3. 点击 "OK" 连接
4. 查看Terminal #0的日志输出

**方法二：使用J-Link Commander**

```bash
# 启动J-Link Commander
JLinkExe

# 连接到目标设备
connect
NRF52833_XXAA
SWD
4000

# 在另一个终端启动RTT Client
JLinkRTTClient
```

**方法三：使用J-Link RTT Logger**

```bash
# 启动RTT Logger，保存日志到文件
JLinkRTTLogger -Device NRF52833_XXAA -if SWD -Speed 4000 -RTTChannel 0 logfile.txt
```

### 3. 查看日志输出

成功连接后，你应该能看到类似以下的日志输出：

```
=== NRF52833 Beacon Started with RTT Logging ===
Device: nrf52833-beacon, Version: 1.0.0
[I][sensor:094]: 'Battery Voltage': Sending state 3.65 V with 2 decimals of accuracy
[D][template.sensor:043]: 'Key Data': Sending state 42.00  with 0 decimals of accuracy
[I][binary_sensor:036]: 'Key State': Sending state OFF
Status - Battery: 3.7V, Key: 42, State: OFF
```

## RTT通道配置

默认配置使用3个上行缓冲区：

- **Channel 0**: 主要日志输出 (1024字节)
- **Channel 1**: 可用于自定义数据
- **Channel 2**: 可用于性能分析

## 故障排除

### 快速检查清单

1. **设备是否正在运行？** 检查LED或其他运行指示
2. **J-Link连接正常？** 使用J-Link Commander测试
3. **RTT Viewer设置正确？** 设备型号、接口、速度
4. **在设备运行时连接？** RTT需要在设备启动后连接

### 常见问题

- **无法连接到目标设备**: 检查硬件连接和设备型号
- **没有日志输出**: 验证RTT配置和缓冲区地址
- **日志输出不完整**: 增加缓冲区大小或降低输出频率

### 详细故障排除

如果RTT仍然不工作，请参考详细的故障排除指南：
**`RTT_TROUBLESHOOTING.md`** - 包含完整的诊断步骤和解决方案

## 高级用法

### 1. 自定义RTT通道

```cpp
// 在C++代码中使用RTT
#include "SEGGER_RTT.h"

// 发送自定义数据到RTT通道1
SEGGER_RTT_printf(1, "Custom data: %d\n", value);
```

### 2. 双向通信

RTT支持从PC向设备发送数据：

```cpp
// 读取RTT输入
char buffer[32];
int bytes = SEGGER_RTT_Read(0, buffer, sizeof(buffer));
if (bytes > 0) {
    // 处理接收到的数据
}
```

### 3. 性能监控

使用RTT进行实时性能监控：

```cpp
// 发送性能数据
SEGGER_RTT_printf(2, "CPU: %d%%, RAM: %d bytes\n", cpu_usage, ram_usage);
```

## 注意事项

1. **功耗影响**: RTT会增加一定的功耗，生产环境建议禁用
2. **缓冲区溢出**: 高频日志可能导致缓冲区溢出，丢失数据
3. **调试器连接**: RTT需要保持J-Link连接，断开后日志停止
4. **多线程安全**: RTT是线程安全的，可在中断中使用

## 相关链接

- [SEGGER RTT官方文档](https://www.segger.com/products/debug-probes/j-link/technology/about-real-time-transfer/)
- [Zephyr RTT配置](https://docs.zephyrproject.org/latest/services/logging/index.html)
- [J-Link软件下载](https://www.segger.com/downloads/jlink/)
