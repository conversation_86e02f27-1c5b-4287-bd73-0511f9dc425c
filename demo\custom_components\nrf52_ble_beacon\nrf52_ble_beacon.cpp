#include "nrf52_ble_beacon.h"

#ifdef USE_NRF52

#include <zephyr/bluetooth/bluetooth.h>
#include <zephyr/bluetooth/hci.h>

namespace esphome {
namespace nrf52_ble_beacon {

void NRF52BLEBeacon::setup() {
  ESP_LOGCONFIG(TAG, "Setting up NRF52 BLE Beacon...");
  
  // Initialize Bluetooth
  int err = bt_enable(nullptr);
  if (err) {
    ESP_LOGE(TAG, "Bluetooth init failed (err %d)", err);
    this->mark_failed();
    return;
  }
  
  ESP_LOGCONFIG(TAG, "Bluetooth initialized successfully");
  
  // Start advertising
  this->start_advertising_();
}

void NRF52BLEBeacon::loop() {
  // Update advertising data periodically
  uint32_t now = millis();
  if (now - this->last_update_ > this->adv_interval_) {
    this->update_advertising_data();
    this->last_update_ = now;
  }
}

void NRF52BLEBeacon::dump_config() {
  ESP_LOGCONFIG(TAG, "NRF52 BLE Beacon:");
  ESP_LOGCONFIG(TAG, "  Device Name: %s", this->device_name_.c_str());
  ESP_LOGCONFIG(TAG, "  Advertising Interval: %d ms", this->adv_interval_);
  ESP_LOGCONFIG(TAG, "  TX Power: %d dBm", this->tx_power_);
}

void NRF52BLEBeacon::start_advertising_() {
  // Set up advertising parameters
  struct bt_le_adv_param adv_param = {
    .id = BT_ID_DEFAULT,
    .sid = 0,
    .secondary_max_skip = 0,
    .options = BT_LE_ADV_OPT_USE_IDENTITY,
    .interval_min = BT_GAP_ADV_FAST_INT_MIN_2,
    .interval_max = BT_GAP_ADV_FAST_INT_MAX_2,
    .peer = nullptr,
  };
  
  // Prepare advertising data
  struct bt_data ad[] = {
    BT_DATA_BYTES(BT_DATA_FLAGS, BT_LE_AD_GENERAL | BT_LE_AD_NO_BREDR),
    BT_DATA(BT_DATA_NAME_COMPLETE, this->device_name_.c_str(), this->device_name_.length()),
  };
  
  // Start advertising
  int err = bt_le_adv_start(&adv_param, ad, ARRAY_SIZE(ad), nullptr, 0);
  if (err) {
    ESP_LOGE(TAG, "Advertising failed to start (err %d)", err);
    return;
  }
  
  this->advertising_started_ = true;
  ESP_LOGI(TAG, "BLE advertising started as '%s'", this->device_name_.c_str());
}

void NRF52BLEBeacon::stop_advertising_() {
  if (this->advertising_started_) {
    int err = bt_le_adv_stop();
    if (err) {
      ESP_LOGW(TAG, "Failed to stop advertising (err %d)", err);
    } else {
      this->advertising_started_ = false;
      ESP_LOGD(TAG, "BLE advertising stopped");
    }
  }
}

void NRF52BLEBeacon::update_advertising_data() {
  if (!this->advertising_started_) {
    return;
  }
  
  // Stop current advertising
  this->stop_advertising_();
  
  // Restart advertising with potentially updated data
  this->start_advertising_();
  
  ESP_LOGD(TAG, "Advertising data refreshed");
}

}  // namespace nrf52_ble_beacon
}  // namespace esphome

#endif  // USE_NRF52
