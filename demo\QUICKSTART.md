# NRF52833 Beacon 快速开始指南

## 🚀 5分钟快速上手

### 1. 选择配置文件

**推荐新手从最简配置开始：**

```bash
# 使用最简配置（推荐）
esphome compile nrf52833_simple.yaml
```

### 2. 编译固件

确保你在demo目录中：

```bash
cd demo
esphome compile nrf52833_simple.yaml
```

编译成功后会生成：
- `.esphome/build/nrf52833-beacon/zephyr/zephyr.hex` - 用于初次SWD烧录
- `.esphome/build/nrf52833-beacon/zephyr/app_update.bin` - 用于BLE OTA更新

### 3. 初次烧录

**需要SWD调试器（如J-Link、ST-Link等）**

```bash
# 使用pyocd烧录
pyocd flash -t nrf52833 .esphome/build/nrf52833-beacon/zephyr/zephyr.hex

# 或使用nrfjprog（如果安装了Nordic工具链）
nrfjprog --program .esphome/build/nrf52833-beacon/zephyr/zephyr.hex --sectorerase --verify --reset
```

### 4. 验证运行

设备启动后应该：
- LED闪烁一次（如果配置了LED）
- 开始BLE广播（可用手机BLE扫描工具检测）
- 每60秒输出一次状态日志

### 5. BLE OTA更新

**使用Web工具（推荐）：**

1. 访问：https://mcumgr-web.netlify.app/
2. 点击"Connect"连接设备
3. 选择"Image Upload"
4. 上传`app_update.bin`文件
5. 等待更新完成

## 📋 配置文件对比

| 配置文件 | 功能 | 适用场景 |
|---------|------|----------|
| `nrf52833_simple.yaml` | 基础传感器 + BLE OTA | 新手学习、简单应用 |
| `nrf52833_demo.yaml` | 更多功能 + LED指示 | 标准应用开发 |
| `nrf52833_beacon_full.yaml` | 自定义BLE beacon | 完整beacon功能 |

## 🔧 常见问题

### Q: 编译失败 "Component api requires component network"
**A:** 已修复。使用新的配置文件，移除了网络相关组件。

### Q: 找不到esphome命令
**A:** 确保ESPHome已正确安装：
```bash
pip install esphome
# 或在虚拟环境中
python -m esphome compile nrf52833_simple.yaml
```

### Q: 烧录失败
**A:** 检查：
- SWD连接是否正确
- 目标芯片是否为NRF52833
- 调试器驱动是否安装

### Q: BLE OTA连接不上
**A:** 确认：
- 设备正在运行MCUboot
- 设备名称正确（nrf52833-beacon）
- 手机蓝牙已开启

## 🎯 下一步

1. **修改传感器**：替换模板传感器为实际硬件
2. **调整GPIO**：根据实际硬件修改引脚配置
3. **优化功耗**：调整更新间隔和日志级别
4. **自定义BLE**：使用完整版配置实现beacon功能

## 📚 更多信息

- 详细说明：`README.md`
- 项目概览：`project_overview.md`
- 自定义组件：`custom_components/nrf52_beacon/`

---

**遇到问题？** 检查日志输出，大多数问题都会有详细的错误信息。
