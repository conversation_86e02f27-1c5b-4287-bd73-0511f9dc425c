esphome:
  name: nrf52833-beacon
  friendly_name: "NRF52833 Beacon Demo"
  comment: "Battery powered beacon with BLE OTA support"
  
  # Configuration for low power operation
  on_boot:
    priority: 600
    then:
      - logger.log: "NRF52833 Beacon starting up"
      - light.turn_on: 
          id: status_led
          effect: "Pulse"
      - delay: 2s
      - light.turn_off: status_led
      
  on_shutdown:
    then:
      - logger.log: "Entering shutdown"
      - light.turn_off: status_led

# NRF52833 platform configuration with MCUboot for BLE OTA
nrf52:
  board: adafruit_feather_nrf52840  # Use compatible board, adjust for NRF52833
  bootloader: mcuboot  # Required for BLE OTA support
  # This will generate app_update.bin for mcumgr-web BLE OTA

# Logging configuration - reduced level for battery saving
logger:
  level: INFO
  # Disable UART logging to save power
  baud_rate: 0

# API and OTA are not available on NRF52 without network connectivity
# NRF52 uses BLE OTA via mcumgr instead

# BLE configuration for NRF52 - using Zephyr BLE stack
# Note: BLE beacon functionality will need custom implementation
# This configuration focuses on the sensor data and OTA capability

# Power management for battery operation
# Using interval-based approach for power optimization

# Battery monitoring
sensor:
  # Battery voltage monitoring using NRF52 ADC
  - platform: adc
    pin: VDD  # NRF52 internal VDD measurement
    name: "Battery Voltage"
    id: battery_voltage
    update_interval: 60s
    accuracy_decimals: 2
    filters:
      - multiply: 3.6  # Adjust based on your setup
    on_value:
      then:
        - if:
            condition:
              lambda: 'return x < 3.2;'  # Low battery threshold
            then:
              - logger.log: "Low battery detected!"
              # Could trigger sleep mode or low power state

  # Example key data sensor (replace with your actual sensor)
  - platform: template
    name: "Key Data"
    id: key_data
    update_interval: 10s
    lambda: |-
      // Replace this with your actual key data logic
      static int counter = 0;
      counter++;
      return counter % 100;
    on_value:
      then:
        # Update beacon data with key value
        - lambda: |-
            // Store key data for BLE advertising
            // Will be used in custom BLE advertising implementation
            ESP_LOGI("beacon", "Key data updated: %d", (int)x);

# Binary sensor for key state
binary_sensor:
  - platform: template
    name: "Key State"
    id: key_state
    lambda: |-
      // Replace with your actual key detection logic
      return id(key_data).state > 50;

# Text sensor for device info
text_sensor:
  - platform: template
    name: "Device Status"
    id: device_status
    update_interval: 30s
    lambda: |-
      char buffer[64];
      snprintf(buffer, sizeof(buffer), "Bat:%.1fV Key:%d", 
               id(battery_voltage).state, 
               (int)id(key_data).state);
      return std::string(buffer);

# Global variables for beacon data
globals:
  - id: beacon_minor
    type: int
    initial_value: '1'

# Interval component for periodic tasks
interval:
  - interval: 30s
    then:
      - logger.log: 
          format: "Beacon alive - Battery: %.1fV, Key: %d"
          args: [ 'id(battery_voltage).state', '(int)id(key_data).state' ]

# Button for manual wake-up (if available on your board)
button:
  - platform: template
    name: "Manual Update"
    on_press:
      - logger.log: "Manual update triggered"
      - component.update: key_data
      - component.update: battery_voltage

# Status LED (optional, disable for maximum battery life)
light:
  - platform: binary
    name: "Status LED"
    id: status_led
    output: status_output
    # Auto turn off after 1 second to save battery
    effects:
      - pulse:
          name: "Pulse"
          transition_length: 0.5s
          update_interval: 1s

output:
  - platform: gpio
    pin: 
      number: 13  # Adjust pin number for your board
      mode: OUTPUT
    id: status_output

# Time component - using internal RTC
# Note: NRF52 doesn't have WiFi, so no SNTP
# Time will need to be set via BLE or other means

# BLE OTA Instructions:
# 1. Compile this configuration with ESPHome
# 2. Flash initially via USB/SWD using the generated .hex file
# 3. For subsequent updates:
#    - Use mcumgr-web (https://mcumgr-web.netlify.app/)
#    - Connect via BLE to the device
#    - Upload the generated app_update.bin file
# 4. The device will reboot and apply the update via MCUboot
#
# Note: BLE beacon functionality requires custom implementation
# as ESPHome doesn't have built-in NRF52 BLE beacon support yet.
# You may need to implement custom C++ code for BLE advertising.
