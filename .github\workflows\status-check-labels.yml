name: Status check labels

on:
  pull_request:
    types: [labeled, unlabeled]

jobs:
  check:
    name: Check ${{ matrix.label }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        label:
          - needs-docs
          - merge-after-release
    steps:
      - name: Check for ${{ matrix.label }} label
        uses: actions/github-script@v7.0.1
        with:
          script: |
            const { data: labels } = await github.rest.issues.listLabelsOnIssue({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number
            });
            const hasLabel = labels.find(label => label.name === '${{ matrix.label }}');
            if (hasLabel) {
              core.setFailed('Pull request cannot be merged, it is labeled as ${{ matrix.label }}');
            }
